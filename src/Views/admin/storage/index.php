<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();

// Helper functions
if (!function_exists('getFileIcon')) {
    function getFileIcon($filename) {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

        $icons = [
            'pdf' => '📄',
            'doc' => '📝', 'docx' => '📝',
            'xls' => '📊', 'xlsx' => '📊',
            'ppt' => '📽️', 'pptx' => '📽️',
            'jpg' => '🖼️', 'jpeg' => '🖼️', 'png' => '🖼️', 'gif' => '🖼️',
            'mp4' => '🎬', 'avi' => '🎬', 'mov' => '🎬',
            'mp3' => '🎵', 'wav' => '🎵',
            'zip' => '📦', 'rar' => '📦', '7z' => '📦',
            'txt' => '📄', 'md' => '📄',
            'html' => '🌐', 'css' => '🎨', 'js' => '⚙️'
        ];

        return $icons[$extension] ?? '📄';
    }
}

if (!function_exists('formatFileSize')) {
    function formatFileSize($bytes) {
        if ($bytes == 0) return '0 B';

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $i = floor(log($bytes) / log(1024));

        return round($bytes / pow(1024, $i), 2) . ' ' . $units[$i];
    }
}

ob_start();
?>

<div class="storage-container">
    <!-- Toolbar -->
    <div class="card mb-3">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <!-- Breadcrumb Navigation -->
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <?php foreach ($breadcrumbs as $index => $crumb): ?>
                                <?php if ($index === count($breadcrumbs) - 1): ?>
                                    <li class="breadcrumb-item active"><?= htmlspecialchars($crumb['name']) ?></li>
                                <?php else: ?>
                                    <li class="breadcrumb-item">
                                        <a href="/admin/storage?path=<?= urlencode($crumb['path']) ?>">
                                            <?= htmlspecialchars($crumb['name']) ?>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </ol>
                    </nav>
                </div>
                
                <div class="col-md-6 text-right">
                    <!-- Action Buttons -->
                    <div class="btn-group" role="group">
                        <?php if ($app->hasPermission('storage.upload')): ?>
                            <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#uploadModal">
                                📤 Качи файлове
                            </button>
                        <?php endif; ?>

                        <?php if ($app->hasPermission('storage.create')): ?>
                            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#createFolderModal">
                                📁 Нова папка
                            </button>
                        <?php endif; ?>
                        
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle" data-toggle="dropdown">
                                👁️ Изглед
                            </button>
                            <div class="dropdown-menu">
                                <a class="dropdown-item <?= $view === 'list' ? 'active' : '' ?>" 
                                   href="?path=<?= urlencode($currentPath) ?>&view=list">
                                    📋 Списък
                                </a>
                                <a class="dropdown-item <?= $view === 'grid' ? 'active' : '' ?>" 
                                   href="?path=<?= urlencode($currentPath) ?>&view=grid">
                                    🔲 Мрежа
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- File Browser -->
    <div class="card">
        <div class="card-body">
            <?php if (empty($items)): ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-folder-open" style="font-size: 4rem; color: #dee2e6;"></i>
                    </div>
                    <h5 class="text-muted">Папката е празна</h5>
                    <p class="text-muted">Няма файлове или папки в тази директория</p>
                </div>
            <?php else: ?>
                <?php if ($view === 'list'): ?>
                    <!-- List View -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Име</th>
                                    <th>Размер</th>
                                    <th>Последна промяна</th>
                                    <th>Действия</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($items as $item): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="mr-2">
                                                    <?php if ($item['is_directory']): ?>
                                                        📁
                                                    <?php else: ?>
                                                        <?= getFileIcon($item['name']) ?>
                                                    <?php endif; ?>
                                                </span>
                                                
                                                <?php if ($item['is_directory']): ?>
                                                    <a href="/admin/storage?path=<?= urlencode($item['path']) ?>" 
                                                       class="text-decoration-none">
                                                        <strong><?= htmlspecialchars($item['name']) ?></strong>
                                                    </a>
                                                <?php else: ?>
                                                    <span><?= htmlspecialchars($item['name']) ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if (!$item['is_directory']): ?>
                                                <?= formatFileSize($item['size']) ?>
                                            <?php else: ?>
                                                <span class="text-muted">—</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($item['last_modified']): ?>
                                                <?= date('d.m.Y H:i', strtotime($item['last_modified'])) ?>
                                            <?php else: ?>
                                                <span class="text-muted">—</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <?php if (!$item['is_directory'] && $app->hasPermission('storage.download')): ?>
                                                    <a href="/admin/storage/download?path=<?= urlencode($item['path']) ?>" 
                                                       class="btn btn-outline-primary btn-sm" title="Изтегли">
                                                        💾
                                                    </a>
                                                <?php endif; ?>
                                                
                                                <?php if ($app->hasPermission('storage.edit')): ?>
                                                    <button type="button" class="btn btn-outline-warning btn-sm"
                                                            data-bs-toggle="modal" data-bs-target="#renameModal"
                                                            onclick="prepareRename('<?= htmlspecialchars($item['path']) ?>', '<?= htmlspecialchars($item['name']) ?>')"
                                                            title="Преименувай">
                                                        ✏️
                                                    </button>
                                                <?php endif; ?>
                                                
                                                <?php if ($app->hasPermission('storage.delete')): ?>
                                                    <button type="button" class="btn btn-outline-danger btn-sm" 
                                                            onclick="confirmDelete('<?= htmlspecialchars($item['path']) ?>', '<?= htmlspecialchars($item['name']) ?>')" 
                                                            title="Изтрий">
                                                        🗑️
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <!-- Grid View -->
                    <div class="row">
                        <?php foreach ($items as $item): ?>
                            <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
                                <div class="card h-100 file-item">
                                    <div class="card-body text-center p-2">
                                        <div class="file-icon mb-2" style="font-size: 2.5rem;">
                                            <?php if ($item['is_directory']): ?>
                                                📁
                                            <?php else: ?>
                                                <?= getFileIcon($item['name']) ?>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="file-name">
                                            <?php if ($item['is_directory']): ?>
                                                <a href="/admin/storage?path=<?= urlencode($item['path']) ?>" 
                                                   class="text-decoration-none">
                                                    <small><strong><?= htmlspecialchars($item['name']) ?></strong></small>
                                                </a>
                                            <?php else: ?>
                                                <small><?= htmlspecialchars($item['name']) ?></small>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <?php if (!$item['is_directory']): ?>
                                            <div class="file-size text-muted">
                                                <small><?= formatFileSize($item['size']) ?></small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="card-footer p-1">
                                        <div class="btn-group btn-group-sm w-100">
                                            <?php if (!$item['is_directory'] && $app->hasPermission('storage.download')): ?>
                                                <a href="/admin/storage/download?path=<?= urlencode($item['path']) ?>" 
                                                   class="btn btn-outline-primary btn-sm">💾</a>
                                            <?php endif; ?>
                                            
                                            <?php if ($app->hasPermission('storage.edit')): ?>
                                                <button type="button" class="btn btn-outline-warning btn-sm"
                                                        data-bs-toggle="modal" data-bs-target="#renameModal"
                                                        onclick="prepareRename('<?= htmlspecialchars($item['path']) ?>', '<?= htmlspecialchars($item['name']) ?>')">✏️</button>
                                            <?php endif; ?>
                                            
                                            <?php if ($app->hasPermission('storage.delete')): ?>
                                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                                        onclick="confirmDelete('<?= htmlspecialchars($item['path']) ?>', '<?= htmlspecialchars($item['name']) ?>')">🗑️</button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<?php if ($app->hasPermission('storage.upload')): ?>
<div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadModalLabel">Качване на файлове</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                    <input type="hidden" name="path" value="<?= htmlspecialchars($currentPath) ?>">

                    <!-- Drag & Drop Zone -->
                    <div class="upload-drop-zone" id="dropZone" onclick="document.getElementById('fileInput').click()">
                        <div class="upload-icon">📤</div>
                        <h5>Пуснете файловете тук</h5>
                        <p class="text-muted mb-2">или кликнете за да изберете файлове</p>
                        <small class="text-muted">Поддържа множество файлове</small>
                    </div>

                    <input type="file" name="files[]" id="fileInput" class="d-none" multiple>

                    <!-- Selected Files List -->
                    <div id="selectedFiles" class="mt-3 d-none">
                        <h6>Избрани файлове:</h6>
                        <div class="file-list" id="fileList"></div>
                    </div>

                    <!-- Upload Progress -->
                    <div id="uploadProgress" class="mt-3 d-none">
                        <h6>Прогрес на качването:</h6>
                        <div class="progress mb-2">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="uploadStatus" class="text-center"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отказ</button>
                <button type="button" class="btn btn-success" onclick="uploadFiles()">Качи файлове</button>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Create Folder Modal -->
<?php if ($app->hasPermission('storage.create')): ?>
<div class="modal fade" id="createFolderModal" tabindex="-1" aria-labelledby="createFolderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createFolderModalLabel">Създаване на нова папка</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createFolderForm">
                    <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                    <input type="hidden" name="path" value="<?= htmlspecialchars($currentPath) ?>">

                    <div class="form-group">
                        <label for="folderName">Име на папката:</label>
                        <input type="text" id="folderName" name="folder_name" class="form-control"
                               placeholder="Въведете име на папката" required>
                        <small class="form-text text-muted">
                            Името не може да съдържа: / \ : * ? " &lt; &gt; |
                        </small>
                        <div id="folderNameError" class="invalid-feedback"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отказ</button>
                <button type="button" class="btn btn-primary" onclick="createFolder()">Създай папка</button>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Rename Modal -->
<?php if ($app->hasPermission('storage.edit')): ?>
<div class="modal fade" id="renameModal" tabindex="-1" aria-labelledby="renameModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="renameModalLabel">Преименуване</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="renameForm">
                    <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                    <input type="hidden" id="renameOldPath" name="old_path">

                    <div class="form-group">
                        <label for="newName">Ново име:</label>
                        <input type="text" id="newName" name="new_name" class="form-control"
                               placeholder="Въведете новото име" required>
                        <small class="form-text text-muted">
                            Името не може да съдържа: / \ : * ? " &lt; &gt; |
                        </small>
                        <div id="renameError" class="invalid-feedback"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отказ</button>
                <button type="button" class="btn btn-warning" onclick="renameItem()">Преименувай</button>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
.storage-container .file-item {
    transition: transform 0.2s;
}

.storage-container .file-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.storage-container .file-name {
    word-break: break-word;
    height: 2.5em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}

.storage-container .breadcrumb {
    background: none;
    padding: 0;
}

.storage-container .table td {
    vertical-align: middle;
}

/* Enhanced Modal Styles */
.modal-content {
    border-radius: 10px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0;
    border-bottom: none;
}

.modal-header .close {
    color: white;
    opacity: 0.8;
    text-shadow: none;
}

.modal-header .close:hover {
    opacity: 1;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 1rem 2rem;
}

/* Upload Drop Zone */
.upload-drop-zone {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background: #f8f9fa;
}

.upload-drop-zone:hover,
.upload-drop-zone.dragover {
    border-color: #007bff;
    background: #e3f2fd;
    transform: scale(1.02);
}

.upload-drop-zone.dragover {
    border-color: #28a745;
    background: #d4edda;
}

.upload-drop-zone .upload-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.upload-drop-zone.dragover .upload-icon {
    color: #28a745;
}

/* Progress Bar Enhancements */
.progress {
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* Form Enhancements */
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Button Enhancements */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* File List Enhancements */
.file-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.5rem;
    background: #f8f9fa;
}

.file-list-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: 4px;
    margin-bottom: 0.25rem;
    background: white;
    border: 1px solid #e9ecef;
}

.file-list-item:last-child {
    margin-bottom: 0;
}

.file-list-item .file-icon {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.file-list-item .file-name {
    flex: 1;
    font-size: 0.9rem;
}

.file-list-item .file-size {
    color: #6c757d;
    font-size: 0.8rem;
}

/* Animation for modal appearance */
.modal.fade .modal-dialog {
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.modal.show .modal-dialog {
    transform: scale(1);
}
</style>

<script>
// Bootstrap modal instances
let uploadModal, createFolderModal, renameModal;

// Initialize modals when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap modals
    const uploadModalEl = document.getElementById('uploadModal');
    const createFolderModalEl = document.getElementById('createFolderModal');
    const renameModalEl = document.getElementById('renameModal');

    if (uploadModalEl) {
        uploadModal = new bootstrap.Modal(uploadModalEl);
        uploadModalEl.addEventListener('shown.bs.modal', function() {
            resetUploadForm();
        });
    }

    if (createFolderModalEl) {
        createFolderModal = new bootstrap.Modal(createFolderModalEl);
        createFolderModalEl.addEventListener('shown.bs.modal', function() {
            const folderNameInput = document.getElementById('folderName');
            folderNameInput.value = '';
            folderNameInput.classList.remove('is-invalid');
            document.getElementById('folderNameError').textContent = '';
            folderNameInput.focus();
        });
    }

    if (renameModalEl) {
        renameModal = new bootstrap.Modal(renameModalEl);
    }
});

// Upload functionality
function resetUploadForm() {
    const fileInput = document.getElementById('fileInput');
    const selectedFiles = document.getElementById('selectedFiles');
    const uploadProgress = document.getElementById('uploadProgress');
    const dropZone = document.getElementById('dropZone');

    if (fileInput) fileInput.value = '';
    if (selectedFiles) selectedFiles.classList.add('d-none');
    if (uploadProgress) uploadProgress.classList.add('d-none');
    if (dropZone) dropZone.classList.remove('dragover');

    // Reset progress bar
    const progressBar = uploadProgress?.querySelector('.progress-bar');
    if (progressBar) {
        progressBar.style.width = '0%';
        progressBar.classList.remove('bg-success', 'bg-danger');
    }
}

function displaySelectedFiles(files) {
    const selectedFiles = document.getElementById('selectedFiles');
    const fileList = document.getElementById('fileList');

    if (files.length === 0) {
        selectedFiles.classList.add('d-none');
        return;
    }

    selectedFiles.classList.remove('d-none');
    fileList.innerHTML = '';

    Array.from(files).forEach(file => {
        const fileItem = document.createElement('div');
        fileItem.className = 'file-list-item';

        const icon = getFileIconForType(file.type, file.name);
        const size = formatBytes(file.size);

        fileItem.innerHTML = `
            <span class="file-icon">${icon}</span>
            <span class="file-name">${file.name}</span>
            <span class="file-size">${size}</span>
        `;

        fileList.appendChild(fileItem);
    });
}

function getFileIconForType(type, name) {
    const extension = name.split('.').pop().toLowerCase();
    const icons = {
        'pdf': '📄', 'doc': '📝', 'docx': '📝', 'xls': '📊', 'xlsx': '📊',
        'ppt': '📽️', 'pptx': '📽️', 'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️',
        'gif': '🖼️', 'mp4': '🎬', 'avi': '🎬', 'mov': '🎬', 'mp3': '🎵',
        'wav': '🎵', 'zip': '📦', 'rar': '📦', '7z': '📦', 'txt': '📄',
        'md': '📄', 'html': '🌐', 'css': '🎨', 'js': '⚙️'
    };
    return icons[extension] || '📄';
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function uploadFiles() {
    const fileInput = document.getElementById('fileInput');
    const files = fileInput.files;

    if (files.length === 0) {
        alert('Моля изберете файлове за качване');
        return;
    }

    const form = document.getElementById('uploadForm');
    const formData = new FormData(form);
    const progressDiv = document.getElementById('uploadProgress');
    const progressBar = progressDiv.querySelector('.progress-bar');
    const statusDiv = document.getElementById('uploadStatus');

    progressDiv.classList.remove('d-none');
    progressBar.style.width = '0%';
    statusDiv.textContent = `Качване на ${files.length} файл(а)...`;

    // Simulate progress for better UX
    let progress = 0;
    const progressInterval = setInterval(() => {
        if (progress < 90) {
            progress += Math.random() * 10;
            progressBar.style.width = Math.min(progress, 90) + '%';
        }
    }, 200);

    fetch('/admin/storage/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        clearInterval(progressInterval);

        if (data.success) {
            progressBar.style.width = '100%';
            progressBar.classList.add('bg-success');
            statusDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${data.message}`;

            setTimeout(() => {
                if (uploadModal) uploadModal.hide();
                location.reload();
            }, 1500);
        } else {
            progressBar.classList.add('bg-danger');
            statusDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> Грешка: ${data.error}`;
        }
    })
    .catch(error => {
        clearInterval(progressInterval);
        progressBar.classList.add('bg-danger');
        statusDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> Грешка при качване: ${error.message}`;
    });
}

// Create folder functionality - removed showCreateFolderModal as it's handled by Bootstrap

function validateFolderName(name) {
    const invalidChars = /[\/\\:*?"<>|]/;
    const errors = [];

    if (!name || name.trim() === '') {
        errors.push('Името на папката не може да бъде празно');
    } else if (invalidChars.test(name)) {
        errors.push('Името съдържа невалидни символи');
    } else if (name.length > 255) {
        errors.push('Името е твърде дълго (максимум 255 символа)');
    } else if (name.startsWith('.') || name.endsWith('.')) {
        errors.push('Името не може да започва или завършва с точка');
    }

    return errors;
}

function createFolder() {
    const folderNameInput = document.getElementById('folderName');
    const folderName = folderNameInput.value.trim();
    const errorDiv = document.getElementById('folderNameError');

    // Validate folder name
    const errors = validateFolderName(folderName);
    if (errors.length > 0) {
        folderNameInput.classList.add('is-invalid');
        errorDiv.textContent = errors[0];
        return;
    }

    folderNameInput.classList.remove('is-invalid');
    errorDiv.textContent = '';

    const form = document.getElementById('createFolderForm');
    const formData = new FormData(form);

    // Disable button during request
    const createButton = document.querySelector('#createFolderModal .btn-primary');
    const originalText = createButton.textContent;
    createButton.disabled = true;
    createButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Създаване...';

    fetch('/admin/storage/create-folder', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (createFolderModal) createFolderModal.hide();
            location.reload();
        } else {
            folderNameInput.classList.add('is-invalid');
            errorDiv.textContent = data.error;
        }
    })
    .catch(error => {
        folderNameInput.classList.add('is-invalid');
        errorDiv.textContent = 'Грешка при създаване на папката: ' + error.message;
    })
    .finally(() => {
        createButton.disabled = false;
        createButton.textContent = originalText;
    });
}

// Rename functionality
function prepareRename(path, currentName) {
    document.getElementById('renameOldPath').value = path;
    const newNameInput = document.getElementById('newName');
    newNameInput.value = currentName;
    newNameInput.classList.remove('is-invalid');
    document.getElementById('renameError').textContent = '';

    // Set up event listener for when modal is shown
    const renameModalEl = document.getElementById('renameModal');
    renameModalEl.addEventListener('shown.bs.modal', function selectText() {
        // Select filename without extension for easier editing
        const dotIndex = currentName.lastIndexOf('.');
        if (dotIndex > 0) {
            newNameInput.setSelectionRange(0, dotIndex);
        } else {
            newNameInput.select();
        }
        newNameInput.focus();

        // Remove this event listener after use
        renameModalEl.removeEventListener('shown.bs.modal', selectText);
    });
}

function renameItem() {
    const newNameInput = document.getElementById('newName');
    const newName = newNameInput.value.trim();
    const errorDiv = document.getElementById('renameError');

    // Validate new name
    const errors = validateFolderName(newName);
    if (errors.length > 0) {
        newNameInput.classList.add('is-invalid');
        errorDiv.textContent = errors[0];
        return;
    }

    newNameInput.classList.remove('is-invalid');
    errorDiv.textContent = '';

    const form = document.getElementById('renameForm');
    const formData = new FormData(form);

    // Disable button during request
    const renameButton = document.querySelector('#renameModal .btn-warning');
    const originalText = renameButton.textContent;
    renameButton.disabled = true;
    renameButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Преименуване...';

    fetch('/admin/storage/rename', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (renameModal) renameModal.hide();
            location.reload();
        } else {
            newNameInput.classList.add('is-invalid');
            errorDiv.textContent = data.error;
        }
    })
    .catch(error => {
        newNameInput.classList.add('is-invalid');
        errorDiv.textContent = 'Грешка при преименуване: ' + error.message;
    })
    .finally(() => {
        renameButton.disabled = false;
        renameButton.textContent = originalText;
    });
}

// Delete functionality
function confirmDelete(path, name) {
    if (confirm(`Сигурни ли сте, че искате да изтриете "${name}"?`)) {
        const formData = new FormData();
        formData.append('_token', '<?= $csrf_token ?>');
        formData.append('path', path);
        
        fetch('/admin/storage/delete', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Грешка: ' + data.error);
            }
        })
        .catch(error => {
            alert('Грешка при изтриване: ' + error.message);
        });
    }
}

// Additional event listeners
document.addEventListener('DOMContentLoaded', function() {

    // File input change handler
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            displaySelectedFiles(this.files);
        });
    }

    // Drag and drop functionality
    const dropZone = document.getElementById('dropZone');
    if (dropZone) {
        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });

        // Highlight drop zone when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        // Handle dropped files
        dropZone.addEventListener('drop', handleDrop, false);
    }

    // Form validation for create folder
    const createFolderForm = document.getElementById('createFolderForm');
    if (createFolderForm) {
        createFolderForm.addEventListener('submit', function(e) {
            e.preventDefault();
            createFolder();
        });

        const folderNameInput = document.getElementById('folderName');
        if (folderNameInput) {
            folderNameInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    createFolder();
                }
            });
        }
    }

    // Form validation for rename
    const renameForm = document.getElementById('renameForm');
    if (renameForm) {
        renameForm.addEventListener('submit', function(e) {
            e.preventDefault();
            renameItem();
        });

        const newNameInput = document.getElementById('newName');
        if (newNameInput) {
            newNameInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    renameItem();
                }
            });
        }
    }
});

// Drag and drop helper functions
function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

function highlight(e) {
    const dropZone = document.getElementById('dropZone');
    dropZone.classList.add('dragover');
}

function unhighlight(e) {
    const dropZone = document.getElementById('dropZone');
    dropZone.classList.remove('dragover');
}

function handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;

    const fileInput = document.getElementById('fileInput');
    fileInput.files = files;

    displaySelectedFiles(files);
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
