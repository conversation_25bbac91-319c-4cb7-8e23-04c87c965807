<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="card">
    <div class="card-header">
        <h3>Редактиране на задача: <?= htmlspecialchars($task->title) ?></h3>
    </div>
    
    <div class="card-body">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($errors) && !empty($errors)): ?>
            <div class="alert alert-danger">
                <ul style="margin: 0; padding-left: 20px;">
                    <?php foreach ($errors as $field => $error): ?>
                        <li><?= htmlspecialchars($error) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="/admin/tasks/<?= $task->id ?>">
            <input type="hidden" name="_method" value="PUT">
            <input type="hidden" name="_token" value="<?= $csrf_token ?>">
            
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group">
                        <label for="title">Заглавие *</label>
                        <input
                            type="text"
                            id="title"
                            name="title"
                            class="form-control"
                            value="<?= htmlspecialchars($old_input['title'] ?? $task->title) ?>"
                            required
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Описание</label>
                        <textarea
                            id="description"
                            name="description"
                            class="form-control"
                            rows="4"
                            placeholder="Детайлно описание на задачата..."
                        ><?= htmlspecialchars($old_input['description'] ?? $task->description) ?></textarea>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="task_type_id">Тип задача *</label>
                        <select id="task_type_id" name="task_type_id" class="form-control" required>
                            <option value="">Изберете тип</option>
                            <?php foreach ($taskTypes as $type): ?>
                                <option
                                    value="<?= $type->id ?>"
                                    <?= ($old_input['task_type_id'] ?? $task->task_type_id) == $type->id ? 'selected' : '' ?>
                                >
                                    <?= htmlspecialchars($type->name) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="status_id">Статус *</label>
                        <select id="status_id" name="status_id" class="form-control" required>
                            <option value="">Изберете статус</option>
                            <?php foreach ($taskStatuses as $status): ?>
                                <option
                                    value="<?= $status->id ?>"
                                    <?= ($old_input['status_id'] ?? $task->status_id) == $status->id ? 'selected' : '' ?>
                                >
                                    <?= htmlspecialchars($status->name) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="priority">Приоритет *</label>
                        <select id="priority" name="priority" class="form-control" required>
                            <option value="">Изберете приоритет</option>
                            <option value="low" <?= ($old_input['priority'] ?? $task->priority) === 'low' ? 'selected' : '' ?>>Нисък</option>
                            <option value="normal" <?= ($old_input['priority'] ?? $task->priority) === 'normal' ? 'selected' : '' ?>>Нормален</option>
                            <option value="high" <?= ($old_input['priority'] ?? $task->priority) === 'high' ? 'selected' : '' ?>>Висок</option>
                            <option value="urgent" <?= ($old_input['priority'] ?? $task->priority) === 'urgent' ? 'selected' : '' ?>>Спешно</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="assigned_to">Основен изпълнител</label>
                        <select id="assigned_to" name="assigned_to" class="form-control">
                            <option value="">Неназначен</option>
                            <?php foreach ($users as $user): ?>
                                <option
                                    value="<?= $user->id ?>"
                                    <?= ($old_input['assigned_to'] ?? $task->assigned_to) == $user->id ? 'selected' : '' ?>
                                >
                                    <?= htmlspecialchars($user->getFullName()) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="start_date">Начална дата</label>
                        <input
                            type="date"
                            id="start_date"
                            name="start_date"
                            class="form-control"
                            value="<?= htmlspecialchars($old_input['start_date'] ?? $task->start_date) ?>"
                        >
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="due_date">Краен срок</label>
                        <input
                            type="date"
                            id="due_date"
                            name="due_date"
                            class="form-control"
                            value="<?= htmlspecialchars($old_input['due_date'] ?? $task->due_date) ?>"
                        >
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="estimated_hours">Очаквани часове</label>
                        <input
                            type="number"
                            id="estimated_hours"
                            name="estimated_hours"
                            class="form-control"
                            step="0.5"
                            min="0"
                            value="<?= htmlspecialchars($old_input['estimated_hours'] ?? $task->estimated_hours) ?>"
                        >
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="progress">Прогрес (%)</label>
                        <input
                            type="number"
                            id="progress"
                            name="progress"
                            class="form-control"
                            min="0"
                            max="100"
                            value="<?= htmlspecialchars($old_input['progress'] ?? $task->progress) ?>"
                        >
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <div class="row">
                    <div class="col-md-6">
                        <label>Допълнителни изпълнители (потребители)</label>
                        <div style="max-height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 4px;">
                            <?php foreach ($users as $user): ?>
                                <div class="form-check">
                                    <input 
                                        type="checkbox" 
                                        id="user_<?= $user->id ?>" 
                                        name="assignee_users[]" 
                                        value="<?= $user->id ?>"
                                        class="form-check-input"
                                        <?= in_array($user->id, $old_input['assignee_users'] ?? []) ? 'checked' : '' ?>
                                    >
                                    <label for="user_<?= $user->id ?>" class="form-check-label">
                                        <?= htmlspecialchars($user->getFullName()) ?>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <label>Допълнителни изпълнители (групи)</label>
                        <div style="max-height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 4px;">
                            <?php foreach ($groups as $group): ?>
                                <div class="form-check">
                                    <input 
                                        type="checkbox" 
                                        id="group_<?= $group->id ?>" 
                                        name="assignee_groups[]" 
                                        value="<?= $group->id ?>"
                                        class="form-check-input"
                                        <?= in_array($group->id, $old_input['assignee_groups'] ?? []) ? 'checked' : '' ?>
                                    >
                                    <label for="group_<?= $group->id ?>" class="form-check-label">
                                        <?= htmlspecialchars($group->name) ?>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-group" style="margin-top: 30px;">
                <button type="submit" class="btn btn-success">
                    💾 Запази промените
                </button>
                <a href="/admin/tasks" class="btn btn-secondary">
                    ↩️ Отказ
                </a>
            </div>
        </form>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
