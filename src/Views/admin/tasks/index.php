<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="card">
    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
        <h3>Задачи (<?= $totalTasks ?>)</h3>
        
        <div style="display: flex; gap: 10px; align-items: center;">
            <?php if ($app->hasPermission('tasks.create')): ?>
                <a href="/admin/tasks/create" class="btn btn-success">
                    ➕ Нова задача
                </a>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="card-body" style="border-bottom: 1px solid #dee2e6; background: #f8f9fa;">
        <form method="GET" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; align-items: end;">
            <div>
                <label for="search" style="font-size: 12px; color: #6c757d;">Търсене</label>
                <input 
                    type="text" 
                    id="search"
                    name="search" 
                    placeholder="Заглавие или описание..." 
                    value="<?= htmlspecialchars($search) ?>"
                    class="form-control"
                >
            </div>
            
            <div>
                <label for="status" style="font-size: 12px; color: #6c757d;">Статус</label>
                <select name="status" id="status" class="form-control">
                    <option value="">Всички статуси</option>
                    <?php foreach ($taskStatuses as $status): ?>
                        <option value="<?= $status->id ?>" <?= $statusFilter == $status->id ? 'selected' : '' ?>>
                            <?= htmlspecialchars($status->name) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <label for="type" style="font-size: 12px; color: #6c757d;">Тип</label>
                <select name="type" id="type" class="form-control">
                    <option value="">Всички типове</option>
                    <?php foreach ($taskTypes as $type): ?>
                        <option value="<?= $type->id ?>" <?= $typeFilter == $type->id ? 'selected' : '' ?>>
                            <?= htmlspecialchars($type->name) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <label for="priority" style="font-size: 12px; color: #6c757d;">Приоритет</label>
                <select name="priority" id="priority" class="form-control">
                    <option value="">Всички приоритети</option>
                    <option value="urgent" <?= $priorityFilter === 'urgent' ? 'selected' : '' ?>>Спешно</option>
                    <option value="high" <?= $priorityFilter === 'high' ? 'selected' : '' ?>>Висок</option>
                    <option value="normal" <?= $priorityFilter === 'normal' ? 'selected' : '' ?>>Нормален</option>
                    <option value="low" <?= $priorityFilter === 'low' ? 'selected' : '' ?>>Нисък</option>
                </select>
            </div>
            
            <div>
                <label for="assignee" style="font-size: 12px; color: #6c757d;">Изпълнител</label>
                <select name="assignee" id="assignee" class="form-control">
                    <option value="">Всички изпълнители</option>
                    <?php foreach ($users as $user): ?>
                        <option value="<?= $user->id ?>" <?= $assigneeFilter == $user->id ? 'selected' : '' ?>>
                            <?= htmlspecialchars($user->getFullName()) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div style="display: flex; gap: 5px;">
                <button type="submit" class="btn btn-primary btn-sm">Филтрирай</button>
                <a href="/admin/tasks" class="btn btn-warning btn-sm">Изчисти</a>
            </div>
        </form>
    </div>
    
    <div class="card-body">
        <?php if (empty($tasks)): ?>
            <p style="text-align: center; color: #7f8c8d; padding: 40px;">
                <?= $search ? 'Няма намерени задачи за "' . htmlspecialchars($search) . '"' : 'Няма създадени задачи' ?>
            </p>
        <?php else: ?>
            <div style="overflow-x: auto;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Заглавие</th>
                            <th>Тип</th>
                            <th>Статус</th>
                            <th>Приоритет</th>
                            <th>Прогрес</th>
                            <th>Изпълнител</th>
                            <th>Краен срок</th>
                            <th>Създател</th>
                            <th>Действия</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($tasks as $task): ?>
                            <tr>
                                <td><?= $task->id ?></td>
                                <td>
                                    <a href="/admin/tasks/<?= $task->id ?>" style="text-decoration: none; color: #2c3e50;">
                                        <strong><?= htmlspecialchars($task->title) ?></strong>
                                    </a>
                                    <?php if ($task->description): ?>
                                        <br><small style="color: #7f8c8d;">
                                            <?= htmlspecialchars(substr($task->description, 0, 100)) ?>
                                            <?= strlen($task->description) > 100 ? '...' : '' ?>
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span style="background: <?= $task->type_color ?>; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">
                                        <?= htmlspecialchars($task->type_name) ?>
                                    </span>
                                </td>
                                <td>
                                    <span style="background: <?= $task->status_color ?>; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">
                                        <?= htmlspecialchars($task->status_name) ?>
                                    </span>
                                </td>
                                <td>
                                    <span style="color: <?= $task->priority_color ?>; font-weight: bold;">
                                        <?= $task->priority_label ?>
                                    </span>
                                </td>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 5px;">
                                        <div style="width: 60px; height: 8px; background: #e9ecef; border-radius: 4px; overflow: hidden;">
                                            <div style="width: <?= $task->progress ?>%; height: 100%; background: #28a745; transition: width 0.3s;"></div>
                                        </div>
                                        <span style="font-size: 12px;"><?= $task->progress ?>%</span>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($task->assignee_first_name): ?>
                                        <?= htmlspecialchars(trim($task->assignee_first_name . ' ' . $task->assignee_last_name)) ?>
                                    <?php else: ?>
                                        <span style="color: #7f8c8d;">Неназначен</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($task->due_date): ?>
                                        <span style="color: <?= $task->is_overdue ? '#e74c3c' : ($task->days_until_due <= 3 ? '#f39c12' : '#2c3e50') ?>;">
                                            <?= date('d.m.Y', strtotime($task->due_date)) ?>
                                            <?php if ($task->is_overdue): ?>
                                                <br><small>Просрочена</small>
                                            <?php elseif ($task->days_until_due <= 3): ?>
                                                <br><small><?= $task->days_until_due ?> дни</small>
                                            <?php endif; ?>
                                        </span>
                                    <?php else: ?>
                                        <span style="color: #7f8c8d;">Без срок</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?= htmlspecialchars(trim($task->creator_first_name . ' ' . $task->creator_last_name)) ?>
                                </td>
                                <td>
                                    <div style="display: flex; gap: 5px;">
                                        <a href="/admin/tasks/<?= $task->id ?>" class="btn btn-primary btn-sm">
                                            👁️ Преглед
                                        </a>
                                        
                                        <?php if ($app->hasPermission('tasks.edit')): ?>
                                            <a href="/admin/tasks/<?= $task->id ?>/edit" class="btn btn-warning btn-sm">
                                                ✏️ Редактирай
                                            </a>
                                        <?php endif; ?>
                                        
                                        <?php if ($app->hasPermission('tasks.delete')): ?>
                                            <form method="POST" action="/admin/tasks/<?= $task->id ?>" style="display: inline;">
                                                <input type="hidden" name="_method" value="DELETE">
                                                <input type="hidden" name="_token" value="<?= $_SESSION['csrf_token'] ?? '' ?>">
                                                <button 
                                                    type="submit" 
                                                    class="btn btn-danger btn-sm"
                                                    data-confirm-delete="Сигурни ли сте, че искате да изтриете задачата <?= htmlspecialchars($task->title) ?>?"
                                                >
                                                    🗑️ Изтрий
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div style="display: flex; justify-content: center; margin-top: 20px; gap: 5px;">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                        <?php 
                        $params = [];
                        if ($search) $params['search'] = $search;
                        if ($statusFilter) $params['status'] = $statusFilter;
                        if ($typeFilter) $params['type'] = $typeFilter;
                        if ($priorityFilter) $params['priority'] = $priorityFilter;
                        if ($assigneeFilter) $params['assignee'] = $assigneeFilter;
                        $params['page'] = $i;
                        
                        $url = '/admin/tasks?' . http_build_query($params);
                        ?>
                        <a 
                            href="<?= $url ?>" 
                            class="btn <?= $i === $currentPage ? 'btn-primary' : 'btn-warning' ?> btn-sm"
                        >
                            <?= $i ?>
                        </a>
                    <?php endfor; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
