<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="card">
    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
        <h3>Групи (<?= $totalGroups ?>)</h3>
        
        <div style="display: flex; gap: 10px; align-items: center;">
            <!-- Search form -->
            <form method="GET" style="display: flex; gap: 10px;">
                <input 
                    type="text" 
                    name="search" 
                    placeholder="Търсене..." 
                    value="<?= htmlspecialchars($search) ?>"
                    class="form-control"
                    style="width: 200px;"
                >
                <button type="submit" class="btn btn-primary btn-sm">Търси</button>
                <?php if ($search): ?>
                    <a href="/admin/groups" class="btn btn-warning btn-sm">Изчисти</a>
                <?php endif; ?>
            </form>
            
            <?php if ($app->hasPermission('groups.create')): ?>
                <a href="/admin/groups/create" class="btn btn-success">
                    ➕ Нова група
                </a>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="card-body">
        <?php if (empty($groups)): ?>
            <p style="text-align: center; color: #7f8c8d; padding: 40px;">
                <?= $search ? 'Няма намерени групи за "' . htmlspecialchars($search) . '"' : 'Няма създадени групи' ?>
            </p>
        <?php else: ?>
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Име</th>
                        <th>Описание</th>
                        <th>Потребители</th>
                        <th>Права</th>
                        <th>Статус</th>
                        <th>Създадена</th>
                        <th>Действия</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($groups as $group): ?>
                        <tr>
                            <td><?= $group->id ?></td>
                            <td>
                                <strong><?= htmlspecialchars($group->name) ?></strong>
                            </td>
                            <td>
                                <?php if ($group->description): ?>
                                    <?= htmlspecialchars($group->description) ?>
                                <?php else: ?>
                                    <span style="color: #7f8c8d; font-style: italic;">Няма описание</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span style="background: #3498db; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;">
                                    <?= $group->getUserCount() ?> потребители
                                </span>
                            </td>
                            <td>
                                <?php 
                                $permissions = $group->getPermissions();
                                $permissionCount = count($permissions);
                                ?>
                                <span style="background: #27ae60; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;">
                                    <?= $permissionCount ?> права
                                </span>
                                <?php if ($permissionCount > 0): ?>
                                    <div style="margin-top: 5px;">
                                        <?php 
                                        $modules = [];
                                        foreach ($permissions as $permission) {
                                            $modules[$permission->module] = ($modules[$permission->module] ?? 0) + 1;
                                        }
                                        foreach ($modules as $module => $count):
                                        ?>
                                            <small style="background: #f39c12; color: white; padding: 1px 4px; border-radius: 2px; margin-right: 2px; font-size: 10px;">
                                                <?= htmlspecialchars($module) ?> (<?= $count ?>)
                                            </small>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($group->isActive()): ?>
                                    <span style="color: #27ae60; font-weight: bold;">✓ Активна</span>
                                <?php else: ?>
                                    <span style="color: #e74c3c; font-weight: bold;">✗ Неактивна</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?= date('d.m.Y', strtotime($group->created_at)) ?>
                            </td>
                            <td>
                                <div style="display: flex; gap: 5px;">
                                    <?php if ($app->hasPermission('groups.edit')): ?>
                                        <a href="/admin/groups/<?= $group->id ?>/edit" class="btn btn-warning btn-sm">
                                            ✏️ Редактирай
                                        </a>
                                    <?php endif; ?>
                                    
                                    <?php if ($app->hasPermission('groups.delete') && $group->name !== 'administrators'): ?>
                                        <form method="POST" action="/admin/groups/<?= $group->id ?>" style="display: inline;">
                                            <input type="hidden" name="_method" value="DELETE">
                                            <input type="hidden" name="_token" value="<?= $_SESSION['csrf_token'] ?? '' ?>">
                                            <button 
                                                type="submit" 
                                                class="btn btn-danger btn-sm"
                                                data-confirm-delete="Сигурни ли сте, че искате да изтриете групата <?= htmlspecialchars($group->name) ?>?"
                                                <?= $group->getUserCount() > 0 ? 'disabled title="Групата има потребители"' : '' ?>
                                            >
                                                🗑️ Изтрий
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div style="display: flex; justify-content: center; margin-top: 20px; gap: 5px;">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                        <?php 
                        $url = '/admin/groups?page=' . $i;
                        if ($search) {
                            $url .= '&search=' . urlencode($search);
                        }
                        ?>
                        <a 
                            href="<?= $url ?>" 
                            class="btn <?= $i === $currentPage ? 'btn-primary' : 'btn-warning' ?> btn-sm"
                        >
                            <?= $i ?>
                        </a>
                    <?php endfor; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
