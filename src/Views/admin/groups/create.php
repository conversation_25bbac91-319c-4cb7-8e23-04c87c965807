<?php
ob_start();
?>

<div class="card">
    <div class="card-header">
        <h3>Създаване на нова група</h3>
    </div>
    
    <div class="card-body">
        <?php if (isset($error)): ?>
            <div class="alert alert-error">
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($errors) && !empty($errors)): ?>
            <div class="alert alert-error">
                <ul style="margin: 0; padding-left: 20px;">
                    <?php foreach ($errors as $field => $fieldErrors): ?>
                        <?php foreach ($fieldErrors as $error): ?>
                            <li><?= htmlspecialchars($error) ?></li>
                        <?php endforeach; ?>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="/admin/groups" data-validate>
            <input type="hidden" name="_token" value="<?= $csrf_token ?>">
            
            <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 30px;">
                <div>
                    <div class="form-group">
                        <label for="name">Име на групата *</label>
                        <input 
                            type="text" 
                            id="name" 
                            name="name" 
                            class="form-control" 
                            value="<?= htmlspecialchars($old_input['name'] ?? '') ?>"
                            required
                            minlength="3"
                            maxlength="50"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Описание</label>
                        <textarea 
                            id="description" 
                            name="description" 
                            class="form-control" 
                            rows="4"
                            maxlength="255"
                        ><?= htmlspecialchars($old_input['description'] ?? '') ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>
                            <input 
                                type="checkbox" 
                                name="is_active" 
                                value="1"
                                <?= isset($old_input['is_active']) && $old_input['is_active'] ? 'checked' : 'checked' ?>
                                style="margin-right: 8px;"
                            >
                            Активна група
                        </label>
                    </div>
                </div>
                
                <div>
                    <div class="form-group">
                        <label>Права на групата</label>
                        <div style="max-height: 400px; overflow-y: auto; border: 1px solid #ced4da; padding: 15px; border-radius: 4px; background: #f8f9fa;">
                            <?php if (!empty($permissions)): ?>
                                <?php foreach ($permissions as $module => $modulePermissions): ?>
                                    <div style="margin-bottom: 20px;">
                                        <h4 style="color: #2c3e50; margin-bottom: 10px; padding-bottom: 5px; border-bottom: 2px solid #3498db;">
                                            📁 <?= ucfirst(htmlspecialchars($module)) ?>
                                        </h4>
                                        
                                        <div style="margin-left: 15px;">
                                            <?php foreach ($modulePermissions as $permission): ?>
                                                <label style="display: block; margin-bottom: 8px; font-weight: normal; padding: 5px; border-radius: 3px; background: white;">
                                                    <input 
                                                        type="checkbox" 
                                                        name="permissions[]" 
                                                        value="<?= $permission->id ?>"
                                                        <?= isset($old_input['permissions']) && is_array($old_input['permissions']) && in_array($permission->id, $old_input['permissions']) ? 'checked' : '' ?>
                                                        style="margin-right: 8px;"
                                                    >
                                                    <strong><?= htmlspecialchars($permission->name) ?></strong>
                                                    <?php if ($permission->description): ?>
                                                        <br>
                                                        <small style="color: #7f8c8d; margin-left: 20px;">
                                                            <?= htmlspecialchars($permission->description) ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </label>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p style="color: #7f8c8d; margin: 0; text-align: center; padding: 20px;">
                                    Няма налични права
                                </p>
                            <?php endif; ?>
                        </div>
                        
                        <div style="margin-top: 10px; display: flex; gap: 10px;">
                            <button type="button" onclick="selectAllPermissions()" class="btn btn-primary btn-sm">
                                Избери всички
                            </button>
                            <button type="button" onclick="deselectAllPermissions()" class="btn btn-warning btn-sm">
                                Премахни всички
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-group" style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                <button type="submit" class="btn btn-success">
                    ✅ Създай група
                </button>
                <a href="/admin/groups" class="btn btn-warning" style="margin-left: 10px;">
                    ↩️ Отказ
                </a>
            </div>
        </form>
    </div>
</div>

<script>
function selectAllPermissions() {
    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
    checkboxes.forEach(checkbox => checkbox.checked = true);
}

function deselectAllPermissions() {
    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
    checkboxes.forEach(checkbox => checkbox.checked = false);
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
