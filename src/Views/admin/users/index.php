<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="card">
    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
        <h3>Потребители (<?= $totalUsers ?>)</h3>
        
        <div style="display: flex; gap: 10px; align-items: center;">
            <!-- Search form -->
            <form method="GET" style="display: flex; gap: 10px;">
                <input 
                    type="text" 
                    name="search" 
                    placeholder="Търсене..." 
                    value="<?= htmlspecialchars($search) ?>"
                    class="form-control"
                    style="width: 200px;"
                >
                <button type="submit" class="btn btn-primary btn-sm">Търси</button>
                <?php if ($search): ?>
                    <a href="/admin/users" class="btn btn-warning btn-sm">Изчисти</a>
                <?php endif; ?>
            </form>
            
            <?php if ($app->hasPermission('users.create')): ?>
                <a href="/admin/users/create" class="btn btn-success">
                    ➕ Нов потребител
                </a>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="card-body">
        <?php if (empty($users)): ?>
            <p style="text-align: center; color: #7f8c8d; padding: 40px;">
                <?= $search ? 'Няма намерени потребители за "' . htmlspecialchars($search) . '"' : 'Няма създадени потребители' ?>
            </p>
        <?php else: ?>
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Потребител</th>
                        <th>Имейл</th>
                        <th>Име</th>
                        <th>Групи</th>
                        <th>Статус</th>
                        <th>Последен вход</th>
                        <th>Действия</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                        <tr>
                            <td><?= $user->id ?></td>
                            <td>
                                <strong><?= htmlspecialchars($user->username) ?></strong>
                            </td>
                            <td><?= htmlspecialchars($user->email) ?></td>
                            <td><?= htmlspecialchars($user->getFullName()) ?></td>
                            <td>
                                <?php 
                                $userGroups = $user->getGroups();
                                if (!empty($userGroups)): 
                                ?>
                                    <?php foreach ($userGroups as $group): ?>
                                        <span style="background: #3498db; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px; margin-right: 4px;">
                                            <?= htmlspecialchars($group->name) ?>
                                        </span>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <span style="color: #7f8c8d;">Няма групи</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($user->isActive()): ?>
                                    <span style="color: #27ae60; font-weight: bold;">✓ Активен</span>
                                <?php else: ?>
                                    <span style="color: #e74c3c; font-weight: bold;">✗ Неактивен</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($user->last_login): ?>
                                    <?= date('d.m.Y H:i', strtotime($user->last_login)) ?>
                                <?php else: ?>
                                    <span style="color: #7f8c8d;">Никога</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div style="display: flex; gap: 5px;">
                                    <?php if ($app->hasPermission('users.edit')): ?>
                                        <a href="/admin/users/<?= $user->id ?>/edit" class="btn btn-warning btn-sm">
                                            ✏️ Редактирай
                                        </a>
                                    <?php endif; ?>
                                    
                                    <?php if ($app->hasPermission('users.delete') && $user->id !== $app->getCurrentUser()['id']): ?>
                                        <form method="POST" action="/admin/users/<?= $user->id ?>" style="display: inline;">
                                            <input type="hidden" name="_method" value="DELETE">
                                            <input type="hidden" name="_token" value="<?= $_SESSION['csrf_token'] ?? '' ?>">
                                            <button 
                                                type="submit" 
                                                class="btn btn-danger btn-sm"
                                                data-confirm-delete="Сигурни ли сте, че искате да изтриете потребителя <?= htmlspecialchars($user->getFullName()) ?>?"
                                            >
                                                🗑️ Изтрий
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div style="display: flex; justify-content: center; margin-top: 20px; gap: 5px;">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                        <?php 
                        $url = '/admin/users?page=' . $i;
                        if ($search) {
                            $url .= '&search=' . urlencode($search);
                        }
                        ?>
                        <a 
                            href="<?= $url ?>" 
                            class="btn <?= $i === $currentPage ? 'btn-primary' : 'btn-warning' ?> btn-sm"
                        >
                            <?= $i ?>
                        </a>
                    <?php endfor; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
