<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <!-- Profile Information -->
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">👤 Профилна информация</h4>
                </div>
                <div class="card-body">
                    <form id="profileForm">
                        <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="first_name">Име *</label>
                                    <input type="text" id="first_name" name="first_name" class="form-control" 
                                           value="<?= htmlspecialchars($user->first_name) ?>" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="last_name">Фамилия *</label>
                                    <input type="text" id="last_name" name="last_name" class="form-control" 
                                           value="<?= htmlspecialchars($user->last_name) ?>" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email *</label>
                            <input type="email" id="email" name="email" class="form-control" 
                                   value="<?= htmlspecialchars($user->email) ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="username">Потребителско име</label>
                            <input type="text" id="username" class="form-control" 
                                   value="<?= htmlspecialchars($user->username) ?>" readonly>
                            <small class="form-text text-muted">Потребителското име не може да се променя</small>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            💾 Запази промените
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Change Password -->
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="mb-0">🔒 Смяна на парола</h4>
                </div>
                <div class="card-body">
                    <form id="passwordForm">
                        <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                        
                        <div class="form-group">
                            <label for="current_password">Текуща парола *</label>
                            <input type="password" id="current_password" name="current_password" class="form-control" required>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="new_password">Нова парола *</label>
                                    <input type="password" id="new_password" name="new_password" class="form-control" 
                                           minlength="6" required>
                                    <small class="form-text text-muted">Минимум 6 символа</small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="confirm_password">Потвърди новата парола *</label>
                                    <input type="password" id="confirm_password" name="confirm_password" class="form-control" 
                                           minlength="6" required>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-warning">
                            🔑 Смени паролата
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Account Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">ℹ️ Информация за акаунта</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>Създаден:</strong></td>
                            <td><?= date('d.m.Y H:i', strtotime($user->created_at)) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Последна промяна:</strong></td>
                            <td><?= date('d.m.Y H:i', strtotime($user->updated_at)) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Статус:</strong></td>
                            <td>
                                <span class="badge badge-<?= $user->is_active ? 'success' : 'danger' ?>">
                                    <?= $user->is_active ? 'Активен' : 'Неактивен' ?>
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- Nextcloud Settings -->
            <div class="card mt-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">☁️ Nextcloud настройки</h5>
                    <?php if ($app->hasPermission('nextcloud.manage_personal')): ?>
                        <a href="/admin/profile/nextcloud-settings" class="btn btn-sm btn-outline-primary">
                            ⚙️ Управление
                        </a>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if ($nextcloudSettings): ?>
                        <div class="mb-2">
                            <strong>Server:</strong><br>
                            <small class="text-muted"><?= htmlspecialchars($nextcloudSettings->server_url) ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>Потребител:</strong><br>
                            <small class="text-muted"><?= htmlspecialchars($nextcloudSettings->username) ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>Статус:</strong><br>
                            <?php if ($nextcloudSettings->last_test_result === 'success'): ?>
                                <span class="badge badge-success">✅ Свързан</span>
                            <?php elseif ($nextcloudSettings->last_test_result === 'failed'): ?>
                                <span class="badge badge-danger">❌ Грешка</span>
                            <?php else: ?>
                                <span class="badge badge-warning">⏳ Не е тестван</span>
                            <?php endif; ?>
                        </div>
                        
                        <?php if ($nextcloudSettings->last_tested_at): ?>
                            <div class="mb-2">
                                <strong>Последен тест:</strong><br>
                                <small class="text-muted">
                                    <?= date('d.m.Y H:i', strtotime($nextcloudSettings->last_tested_at)) ?>
                                </small>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($app->hasPermission('nextcloud.test_connection')): ?>
                            <button type="button" class="btn btn-sm btn-info" onclick="testNextcloudConnection()">
                                🔄 Тествай връзката
                            </button>
                        <?php endif; ?>
                        
                    <?php else: ?>
                        <p class="text-muted mb-2">Няма конфигурирани Nextcloud настройки</p>
                        
                        <?php if ($app->hasPermission('nextcloud.manage_personal')): ?>
                            <a href="/admin/profile/nextcloud-settings" class="btn btn-sm btn-primary">
                                ➕ Добави настройки
                            </a>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">⚡ Бързи действия</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if ($app->hasPermission('storage.view')): ?>
                            <a href="/admin/storage" class="btn btn-outline-primary btn-sm">
                                💾 Файлов мениджър
                            </a>
                        <?php endif; ?>
                        
                        <?php if ($app->hasPermission('tasks.view')): ?>
                            <a href="/admin/tasks" class="btn btn-outline-success btn-sm">
                                📋 Моите задачи
                            </a>
                        <?php endif; ?>
                        
                        <a href="/admin" class="btn btn-outline-secondary btn-sm">
                            🏠 Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<div id="messageContainer"></div>

<script>
// Profile form submission
document.getElementById('profileForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('/admin/profile/update', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('success', data.message);
        } else {
            showMessage('error', data.error);
        }
    })
    .catch(error => {
        showMessage('error', 'Грешка при запазване: ' + error.message);
    });
});

// Password form submission
document.getElementById('passwordForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    // Check if passwords match
    const newPassword = formData.get('new_password');
    const confirmPassword = formData.get('confirm_password');
    
    if (newPassword !== confirmPassword) {
        showMessage('error', 'Паролите не съвпадат');
        return;
    }
    
    fetch('/admin/profile/update', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('success', data.message);
            this.reset(); // Clear the form
        } else {
            showMessage('error', data.error);
        }
    })
    .catch(error => {
        showMessage('error', 'Грешка при смяна на паролата: ' + error.message);
    });
});

// Test Nextcloud connection
function testNextcloudConnection() {
    const formData = new FormData();
    formData.append('_token', '<?= $csrf_token ?>');
    
    fetch('/admin/profile/nextcloud/test', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('success', data.message);
            setTimeout(() => location.reload(), 1500);
        } else {
            showMessage('error', data.error);
        }
    })
    .catch(error => {
        showMessage('error', 'Грешка при тестване: ' + error.message);
    });
}

// Show message function
function showMessage(type, message) {
    const container = document.getElementById('messageContainer');
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? '✅' : '❌';
    
    container.innerHTML = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${icon} ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
