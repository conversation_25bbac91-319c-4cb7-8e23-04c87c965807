<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="card">
    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
        <h3>➕ Създаване на трансфер</h3>
        <div style="display: flex; gap: 10px; align-items: center;">
            <a href="/admin/strixbudget/transfers" class="btn btn-outline-secondary">
                ← Назад към списъка
            </a>
        </div>
    </div>
    <div class="card-body">

            <?php if (isset($error)): ?>
                <div class="alert alert-danger" role="alert">
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <?php if (isset($errors) && !empty($errors)): ?>
                <div class="alert alert-danger" role="alert">
                    <ul class="mb-0">
                        <?php foreach ($errors as $field => $error): ?>
                            <li><?= htmlspecialchars(is_array($error) ? implode(', ', $error) : $error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Данни за трансфера</h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="/admin/strixbudget/transfers">
                                <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="from_account_id" class="form-label">От банкова сметка *</label>
                                            <select class="form-select <?= isset($errors['from_account_id']) ? 'is-invalid' : '' ?>" 
                                                    id="from_account_id" 
                                                    name="from_account_id" 
                                                    required>
                                                <option value="">Изберете сметка</option>
                                                <?php foreach ($bankAccounts as $account): ?>
                                                    <option value="<?= $account->id ?>" 
                                                            data-balance="<?= $account->balance ?? 0 ?>"
                                                            data-currency="<?= htmlspecialchars($account->currency) ?>"
                                                            <?= ($old_input['from_account_id'] ?? '') == $account->id ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($account->name) ?> 
                                                        (<?= number_format($account->balance ?? 0, 2) ?> <?= htmlspecialchars($account->currency) ?>)
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="form-text">Сметката, от която ще се извърши трансферът</div>
                                            <?php if (isset($errors['from_account_id'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['from_account_id']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="to_account_id" class="form-label">Към банкова сметка *</label>
                                            <select class="form-select <?= isset($errors['to_account_id']) ? 'is-invalid' : '' ?>" 
                                                    id="to_account_id" 
                                                    name="to_account_id" 
                                                    required>
                                                <option value="">Изберете сметка</option>
                                                <?php foreach ($bankAccounts as $account): ?>
                                                    <option value="<?= $account->id ?>" 
                                                            data-currency="<?= htmlspecialchars($account->currency) ?>"
                                                            <?= ($old_input['to_account_id'] ?? '') == $account->id ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($account->name) ?> (<?= htmlspecialchars($account->currency) ?>)
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="form-text">Сметката, към която ще се извърши трансферът</div>
                                            <?php if (isset($errors['to_account_id'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['to_account_id']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Currency Warning -->
                                <div id="currency-warning" class="alert alert-warning" style="display: none;">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>Внимание:</strong> Избрали сте сметки с различни валути. 
                                    Моля, уверете се, че сумата е правилно конвертирана.
                                </div>

                                <!-- Balance Warning -->
                                <div id="balance-warning" class="alert alert-danger" style="display: none;">
                                    <i class="fas fa-exclamation-circle"></i>
                                    <strong>Внимание:</strong> Сумата надвишава наличния баланс в сметката.
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="amount" class="form-label">Сума *</label>
                                            <div class="input-group">
                                                <input type="number"
                                                       class="form-control <?= isset($errors['amount']) ? 'is-invalid' : '' ?>"
                                                       id="amount"
                                                       name="amount"
                                                       value="<?= htmlspecialchars($old_input['amount'] ?? '') ?>"
                                                       step="0.01"
                                                       min="0.01"
                                                       placeholder="0.00"
                                                       required>
                                                <span class="input-group-text" id="currency-symbol">лв.</span>
                                            </div>
                                            <div class="form-text">Сумата за трансфер</div>
                                            <?php if (isset($errors['amount'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['amount']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="fee" class="form-label">Такса</label>
                                            <div class="input-group">
                                                <input type="number"
                                                       class="form-control <?= isset($errors['fee']) ? 'is-invalid' : '' ?>"
                                                       id="fee"
                                                       name="fee"
                                                       value="<?= htmlspecialchars($old_input['fee'] ?? '0.00') ?>"
                                                       step="0.01"
                                                       min="0"
                                                       placeholder="0.00">
                                                <span class="input-group-text">лв.</span>
                                            </div>
                                            <div class="form-text">Такса за трансфера (ако има)</div>
                                            <?php if (isset($errors['fee'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['fee']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Exchange Rate Field (shown only when currencies differ) -->
                                <div id="exchange-rate-section" class="row" style="display: none;">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="exchange_rate" class="form-label">Обменен курс</label>
                                            <div class="input-group">
                                                <input type="number"
                                                       class="form-control <?= isset($errors['exchange_rate']) ? 'is-invalid' : '' ?>"
                                                       id="exchange_rate"
                                                       name="exchange_rate"
                                                       value="<?= htmlspecialchars($old_input['exchange_rate'] ?? '1.00') ?>"
                                                       step="0.0001"
                                                       min="0.0001"
                                                       placeholder="1.0000">
                                                <span class="input-group-text" id="exchange-rate-label">1 лв. = ? лв.</span>
                                            </div>
                                            <div class="form-text">Курс за конвертиране между валутите</div>
                                            <?php if (isset($errors['exchange_rate'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['exchange_rate']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label class="form-label">Получена сума</label>
                                            <div class="input-group">
                                                <input type="text"
                                                       class="form-control"
                                                       id="converted_amount"
                                                       readonly
                                                       placeholder="0.00">
                                                <span class="input-group-text" id="target-currency">лв.</span>
                                            </div>
                                            <div class="form-text">Сума, която ще бъде получена в целевата валута</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="date" class="form-label">Дата *</label>
                                    <input type="date" 
                                           class="form-control <?= isset($errors['date']) ? 'is-invalid' : '' ?>" 
                                           id="date" 
                                           name="date" 
                                           value="<?= htmlspecialchars($old_input['date'] ?? date('Y-m-d')) ?>"
                                           required>
                                    <div class="form-text">Дата на трансфера</div>
                                    <?php if (isset($errors['date'])): ?>
                                        <div class="invalid-feedback">
                                            <?= htmlspecialchars($errors['date']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="description" class="form-label">Описание *</label>
                                    <input type="text" 
                                           class="form-control <?= isset($errors['description']) ? 'is-invalid' : '' ?>" 
                                           id="description" 
                                           name="description" 
                                           value="<?= htmlspecialchars($old_input['description'] ?? '') ?>"
                                           placeholder="Например: Прехвърляне между сметки, Попълване на спестовна сметка"
                                           required>
                                    <div class="form-text">Кратко описание на трансфера</div>
                                    <?php if (isset($errors['description'])): ?>
                                        <div class="invalid-feedback">
                                            <?= htmlspecialchars($errors['description']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="reference" class="form-label">Референция</label>
                                    <input type="text" 
                                           class="form-control <?= isset($errors['reference']) ? 'is-invalid' : '' ?>" 
                                           id="reference" 
                                           name="reference" 
                                           value="<?= htmlspecialchars($old_input['reference'] ?? '') ?>"
                                           placeholder="Номер на трансфер, банков код и т.н.">
                                    <div class="form-text">Опционално поле за референтен номер</div>
                                    <?php if (isset($errors['reference'])): ?>
                                        <div class="invalid-feedback">
                                            <?= htmlspecialchars($errors['reference']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="notes" class="form-label">Бележки</label>
                                    <textarea class="form-control <?= isset($errors['notes']) ? 'is-invalid' : '' ?>" 
                                              id="notes" 
                                              name="notes" 
                                              rows="3"
                                              placeholder="Допълнителни бележки за трансфера..."><?= htmlspecialchars($old_input['notes'] ?? '') ?></textarea>
                                    <div class="form-text">Опционално поле за допълнителна информация</div>
                                    <?php if (isset($errors['notes'])): ?>
                                        <div class="invalid-feedback">
                                            <?= htmlspecialchars($errors['notes']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Total Amount Display -->
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-calculator"></i> Обобщение на трансфера:</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <strong>Сума за трансфер:</strong><br>
                                            <span id="transfer-amount">0.00 лв.</span>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>Такса:</strong><br>
                                            <span id="transfer-fee">0.00 лв.</span>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>Общо за дебитиране:</strong><br>
                                            <span id="total-amount" class="text-primary">0.00 лв.</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="/admin/strixbudget/transfers" class="btn btn-secondary me-md-2">
                                        Отказ
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        💾 Създаване на трансфер
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-info">Помощ</h6>
                        </div>
                        <div class="card-body">
                            <h6>Съвети за създаване на трансфер:</h6>
                            <ul class="small">
                                <li>Трансферът прехвърля пари между ваши сметки</li>
                                <li>Проверете балансите преди трансфер</li>
                                <li>Внимавайте с различните валути</li>
                                <li>Добавете такса, ако банката я начислява</li>
                                <li>Използвайте описателни имена</li>
                            </ul>

                            <hr>

                            <h6>Важно:</h6>
                            <div class="alert alert-warning small">
                                <i class="fas fa-exclamation-triangle"></i>
                                Трансферът ще намали баланса на изходящата сметка и ще увеличи баланса на входящата сметка.
                            </div>
                        </div>
                    </div>

                    <!-- Account Balances -->
                    <div class="card shadow mt-3">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-success">Баланси на сметки</h6>
                        </div>
                        <div class="card-body">
                            <?php foreach ($bankAccounts as $account): ?>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="small"><?= htmlspecialchars($account->name) ?></span>
                                    <span class="small font-weight-bold">
                                        <?= number_format($account->balance ?? 0, 2) ?> <?= htmlspecialchars($account->currency) ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fromAccountSelect = document.getElementById('from_account_id');
    const toAccountSelect = document.getElementById('to_account_id');
    const amountInput = document.getElementById('amount');
    const feeInput = document.getElementById('fee');
    const currencySymbol = document.getElementById('currency-symbol');
    const currencyWarning = document.getElementById('currency-warning');
    const balanceWarning = document.getElementById('balance-warning');

    function updateCalculations() {
        const amount = parseFloat(amountInput.value) || 0;
        const fee = parseFloat(feeInput.value) || 0;
        const total = amount + fee;

        document.getElementById('transfer-amount').textContent = amount.toFixed(2) + ' лв.';
        document.getElementById('transfer-fee').textContent = fee.toFixed(2) + ' лв.';
        document.getElementById('total-amount').textContent = total.toFixed(2) + ' лв.';

        // Check balance
        const fromAccount = fromAccountSelect.options[fromAccountSelect.selectedIndex];
        if (fromAccount && fromAccount.dataset.balance) {
            const balance = parseFloat(fromAccount.dataset.balance);
            if (total > balance) {
                balanceWarning.style.display = 'block';
            } else {
                balanceWarning.style.display = 'none';
            }
        }
    }

    function checkCurrencies() {
        const fromAccount = fromAccountSelect.options[fromAccountSelect.selectedIndex];
        const toAccount = toAccountSelect.options[toAccountSelect.selectedIndex];
        const exchangeRateSection = document.getElementById('exchange-rate-section');
        const exchangeRateLabel = document.getElementById('exchange-rate-label');
        const targetCurrency = document.getElementById('target-currency');

        if (fromAccount && toAccount && fromAccount.dataset.currency && toAccount.dataset.currency) {
            const fromCurrency = fromAccount.dataset.currency;
            const toCurrency = toAccount.dataset.currency;

            if (fromCurrency !== toCurrency) {
                currencyWarning.style.display = 'block';
                exchangeRateSection.style.display = 'block';
                exchangeRateLabel.textContent = `1 ${fromCurrency} = ? ${toCurrency}`;
                targetCurrency.textContent = toCurrency;
            } else {
                currencyWarning.style.display = 'none';
                exchangeRateSection.style.display = 'none';
            }

            // Update currency symbol
            currencySymbol.textContent = fromCurrency;
        } else {
            exchangeRateSection.style.display = 'none';
        }

        updateConvertedAmount();
    }

    function updateConvertedAmount() {
        const amount = parseFloat(amountInput.value) || 0;
        const exchangeRate = parseFloat(document.getElementById('exchange_rate').value) || 1;
        const convertedAmount = amount * exchangeRate;

        document.getElementById('converted_amount').value = convertedAmount.toFixed(2);
    }

    function preventSameAccount() {
        const fromValue = fromAccountSelect.value;
        const toValue = toAccountSelect.value;

        if (fromValue && toValue && fromValue === toValue) {
            alert('Не можете да изберете една и съща сметка за изходяща и входяща!');
            toAccountSelect.value = '';
        }
    }

    // Event listeners
    fromAccountSelect.addEventListener('change', function() {
        checkCurrencies();
        updateCalculations();
        preventSameAccount();
    });

    toAccountSelect.addEventListener('change', function() {
        checkCurrencies();
        preventSameAccount();
    });

    amountInput.addEventListener('input', function() {
        updateCalculations();
        updateConvertedAmount();
    });
    feeInput.addEventListener('input', updateCalculations);

    // Exchange rate listener
    const exchangeRateInput = document.getElementById('exchange_rate');
    if (exchangeRateInput) {
        exchangeRateInput.addEventListener('input', updateConvertedAmount);
    }

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const fromAccount = fromAccountSelect.value;
            const toAccount = toAccountSelect.value;
            const amount = parseFloat(amountInput.value);
            const description = document.getElementById('description').value.trim();

            if (!fromAccount) {
                alert('Моля изберете изходяща сметка');
                e.preventDefault();
                return;
            }

            if (!toAccount) {
                alert('Моля изберете входяща сметка');
                e.preventDefault();
                return;
            }

            if (fromAccount === toAccount) {
                alert('Изходящата и входящата сметка не могат да бъдат еднакви');
                e.preventDefault();
                return;
            }

            if (!amount || amount <= 0) {
                alert('Моля въведете валидна сума');
                e.preventDefault();
                return;
            }

            if (!description) {
                alert('Моля въведете описание');
                e.preventDefault();
                return;
            }

            // Check balance one more time
            const fromAccountOption = fromAccountSelect.options[fromAccountSelect.selectedIndex];
            if (fromAccountOption && fromAccountOption.dataset.balance) {
                const balance = parseFloat(fromAccountOption.dataset.balance);
                const fee = parseFloat(feeInput.value) || 0;
                const total = amount + fee;

                if (total > balance) {
                    if (!confirm('Сумата надвишава наличния баланс. Продължавате ли?')) {
                        e.preventDefault();
                        return;
                    }
                }
            }
        });
    }

    // Initial calculations
    updateCalculations();
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../../layouts/app.php';
?>
