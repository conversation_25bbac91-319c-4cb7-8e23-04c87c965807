<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="card">
    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
        <h3>🔄 Трансфери</h3>
        <div style="display: flex; gap: 10px; align-items: center;">
            <a href="/admin/strixbudget/transfers/create" class="btn btn-primary">
                ➕ Нов трансфер
            </a>
            <a href="/admin/strixbudget" class="btn btn-outline-secondary">
                ← Назад към dashboard
            </a>
        </div>
    </div>
    <div class="card-body">

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="from_account_id" class="form-label">От сметка</label>
                            <select class="form-select" id="from_account_id" name="from_account_id">
                                <option value="">Всички сметки</option>
                                <?php foreach ($bankAccounts as $account): ?>
                                    <option value="<?= $account->id ?>" 
                                            <?= ($filters['from_account_id'] ?? '') == $account->id ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($account->name) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="to_account_id" class="form-label">Към сметка</label>
                            <select class="form-select" id="to_account_id" name="to_account_id">
                                <option value="">Всички сметки</option>
                                <?php foreach ($bankAccounts as $account): ?>
                                    <option value="<?= $account->id ?>" 
                                            <?= ($filters['to_account_id'] ?? '') == $account->id ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($account->name) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">От дата</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?= htmlspecialchars($filters['date_from'] ?? '') ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">До дата</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?= htmlspecialchars($filters['date_to'] ?? '') ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="search" class="form-label">Търсене</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="Описание..." 
                                       value="<?= htmlspecialchars($filters['search'] ?? '') ?>">
                                <button class="btn btn-outline-secondary" type="submit">🔍</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card text-white bg-primary">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Общо трансфери</h6>
                                    <h4><?= count($transfers) ?></h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exchange-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-white bg-success">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Обща сума</h6>
                                    <h4><?= number_format($stats['total_amount'] ?? 0, 2) ?> лв.</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-coins fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-white bg-info">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Средна сума</h6>
                                    <h4><?= count($transfers) > 0 ? number_format(($stats['total_amount'] ?? 0) / count($transfers), 2) : '0.00' ?> лв.</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calculator fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transfers Table -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Списък с трансфери</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($transfers)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Няма намерени трансфери</h5>
                            <p class="text-muted">Създайте първия си трансфер или променете филтрите.</p>
                            <a href="/admin/strixbudget/transfers/create" class="btn btn-primary">
                                ➕ Създаване на трансфер
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Дата</th>
                                        <th>От сметка</th>
                                        <th>Към сметка</th>
                                        <th>Описание</th>
                                        <th class="text-end">Сума</th>
                                        <th class="text-center">Статус</th>
                                        <th class="text-center">Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($transfers as $transfer): ?>
                                        <tr>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('d.m.Y', strtotime($transfer->date ?? 'now')) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-arrow-left"></i>
                                                    <?= htmlspecialchars($transfer->from_account_name ?? 'Неизвестна сметка') ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-arrow-right"></i>
                                                    <?= htmlspecialchars($transfer->to_account_name ?? 'Неизвестна сметка') ?>
                                                </span>
                                            </td>
                                            <td>
                                                <strong><?= htmlspecialchars($transfer->description ?? 'Без описание') ?></strong>
                                                <?php if ($transfer->reference): ?>
                                                    <br><small class="text-muted">Ref: <?= htmlspecialchars($transfer->reference) ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-end">
                                                <strong class="text-primary">
                                                    <?= number_format($transfer->amount ?? 0, 2) ?> лв.
                                                </strong>
                                                <?php if (($transfer->fee ?? 0) > 0): ?>
                                                    <br><small class="text-muted">
                                                        Такса: <?= number_format($transfer->fee, 2) ?> лв.
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <?php
                                                $status = $transfer->status ?? 'completed';
                                                $statusConfig = [
                                                    'pending' => ['Чакащ', 'warning', 'clock'],
                                                    'completed' => ['Завършен', 'success', 'check'],
                                                    'failed' => ['Неуспешен', 'danger', 'times'],
                                                    'cancelled' => ['Отменен', 'secondary', 'ban']
                                                ];
                                                $config = $statusConfig[$status] ?? $statusConfig['completed'];
                                                ?>
                                                <span class="badge bg-<?= $config[1] ?>">
                                                    <i class="fas fa-<?= $config[2] ?>"></i> <?= $config[0] ?>
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group btn-group-sm">
                                                    <a href="/admin/strixbudget/transfers/<?= $transfer->id ?>" 
                                                       class="btn btn-outline-info" title="Преглед">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if (($transfer->status ?? 'completed') === 'pending'): ?>
                                                        <a href="/admin/strixbudget/transfers/<?= $transfer->id ?>/edit" 
                                                           class="btn btn-outline-warning" title="Редактиране">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button type="button" 
                                                                class="btn btn-outline-danger" 
                                                                title="Отмяна"
                                                                onclick="cancelTransfer(<?= $transfer->id ?>)">
                                                            <i class="fas fa-ban"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button type="button" 
                                                                class="btn btn-outline-danger" 
                                                                title="Изтриване"
                                                                onclick="deleteTransfer(<?= $transfer->id ?>)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination would go here -->
                        <?php if (isset($pagination)): ?>
                            <nav aria-label="Pagination">
                                <ul class="pagination justify-content-center">
                                    <!-- Pagination links -->
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
    </div>
</div>

<script>
function cancelTransfer(id) {
    if (confirm('Сигурни ли сте, че искате да отмените този трансфер?')) {
        fetch(`/admin/strixbudget/transfers/${id}/cancel`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Грешка при отмяна: ' + data.message);
            }
        })
        .catch(error => {
            alert('Грешка при заявката: ' + error.message);
        });
    }
}

function deleteTransfer(id) {
    if (confirm('Сигурни ли сте, че искате да изтриете този трансфер?')) {
        fetch(`/admin/strixbudget/transfers/${id}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Грешка при изтриване: ' + data.message);
            }
        })
        .catch(error => {
            alert('Грешка при заявката: ' + error.message);
        });
    }
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../../layouts/app.php';
?>
