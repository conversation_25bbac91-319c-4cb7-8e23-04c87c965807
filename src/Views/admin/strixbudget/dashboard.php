<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h3>💰 StrixBudget Dashboard</h3>
        <div class="d-flex gap-2">
            <a href="/admin/strixbudget/settings" class="btn btn-outline-secondary">
                ⚙️ Настройки
            </a>
            <a href="/admin" class="btn btn-outline-secondary">
                ← Назад към админ панел
            </a>
        </div>
    </div>
    <div class="card-body">

<?php if (!$isConfigured): ?>
    <div class="text-center py-5">
        <div class="mb-4">
            <i class="fas fa-cog fa-4x text-warning"></i>
        </div>
        <h4 class="text-muted mb-3">StrixBudget не е конфигуриран</h4>
        <p class="text-muted mb-4">
            За да започнете да използвате StrixBudget интеграцията, моля конфигурирайте API настройките.
        </p>
        <a href="/admin/strixbudget/settings" class="btn btn-warning btn-lg">
            <i class="fas fa-wrench me-2"></i>Конфигуриране на настройки
        </a>
    </div>
<?php else: ?>
    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-primary">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Общ баланс</h6>
                            <h4><?= number_format($total_balance ?? 0, 2) ?> EUR</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-wallet fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Банкови сметки</h6>
                            <h4><?= $total_accounts ?? 0 ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-university fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-info">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Транзакции</h6>
                            <h4><?= count($recent_transactions ?? []) ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exchange-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Трансфери</h6>
                            <h4><?= count($recent_transfers ?? []) ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-arrows-alt-h fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="m-0 font-weight-bold text-primary">⚡ Бързи действия</h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <a href="/admin/strixbudget/transactions/create" class="btn btn-success w-100">
                        <i class="fas fa-plus me-2"></i>Нова транзакция
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="/admin/strixbudget/transfers/create" class="btn btn-info w-100">
                        <i class="fas fa-exchange-alt me-2"></i>Нов трансфер
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="/admin/strixbudget/bank-accounts/create" class="btn btn-primary w-100">
                        <i class="fas fa-university me-2"></i>Нова сметка
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="/admin/strixbudget/counterparties/create" class="btn btn-secondary w-100">
                        <i class="fas fa-users me-2"></i>Нов контрагент
                    </a>
                </div>
            </div>
            <div class="row g-3 mt-2">
                <div class="col-md-3">
                    <a href="/admin/strixbudget/transactions" class="btn btn-outline-primary w-100">
                        <i class="fas fa-list me-2"></i>Всички транзакции
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="/admin/strixbudget/transfers" class="btn btn-outline-info w-100">
                        <i class="fas fa-arrows-alt-h me-2"></i>Всички трансфери
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="/admin/strixbudget/counterparties" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-address-book me-2"></i>Всички контрагенти
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="/admin/strixbudget/transaction-types" class="btn btn-outline-dark w-100">
                        <i class="fas fa-tags me-2"></i>Типове транзакции
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- API Status -->
    <?php if (isset($error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Грешка:</strong> <?= htmlspecialchars($error) ?>
        </div>
    <?php else: ?>
        <?php
        $currentUser = $app->getCurrentUser();
        $settings = \Strix\ERP\Models\UserStrixBudgetSettings::getByUserId($currentUser['id']);
        $isMockMode = false;
        if ($settings && $settings->isConfigured()) {
            $config = $settings->getClientConfig();
            $client = new \Strix\ERP\Services\StrixBudgetClient($config['api_url']);
            $isMockMode = $client->isMockMode();
        }
        ?>
        <div class="alert <?= $isMockMode ? 'alert-warning' : 'alert-success' ?>">
            <i class="fas <?= $isMockMode ? 'fa-exclamation-triangle' : 'fa-check-circle' ?> me-2"></i>
            <strong>Статус:</strong> StrixBudget API връзката работи успешно
            <?php if ($isMockMode): ?>
                <br><small><i class="fas fa-info-circle me-1"></i>Работи в демо режим (API сървърът не е достъпен)</small>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Recent Transactions -->
    <?php if (!empty($recent_transactions)): ?>
    <div class="card mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">📊 Последни транзакции</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Дата</th>
                            <th>Тип</th>
                            <th>Сума</th>
                            <th>Описание</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach (array_slice($recent_transactions, 0, 5) as $transaction): ?>
                        <tr>
                            <td>
                                <small class="text-muted">
                                    <?= date('d.m.Y', strtotime($transaction->executed_at ?? 'now')) ?>
                                </small>
                            </td>
                            <td>
                                <span class="badge <?= $transaction->isIncome() ? 'bg-success' : 'bg-danger' ?>">
                                    <i class="fas <?= $transaction->isIncome() ? 'fa-arrow-up' : 'fa-arrow-down' ?>"></i>
                                    <?= $transaction->isIncome() ? 'Приход' : 'Разход' ?>
                                </span>
                            </td>
                            <td class="<?= $transaction->isIncome() ? 'text-success' : 'text-danger' ?>">
                                <strong><?= number_format($transaction->amount ?? 0, 2) ?> <?= htmlspecialchars($transaction->currency ?? 'EUR') ?></strong>
                            </td>
                            <td><?= htmlspecialchars($transaction->description ?? 'Без описание') ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <div class="text-center">
                <a href="/admin/strixbudget/transactions" class="btn btn-outline-primary">
                    <i class="fas fa-list me-2"></i>Виж всички транзакции
                </a>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Recent Transfers -->
    <?php if (!empty($recent_transfers)): ?>
    <div class="card mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">🔄 Последни трансфери</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Дата</th>
                            <th>От сметка</th>
                            <th>Към сметка</th>
                            <th>Сума</th>
                            <th>Описание</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach (array_slice($recent_transfers, 0, 5) as $transfer): ?>
                        <tr>
                            <td>
                                <small class="text-muted">
                                    <?= date('d.m.Y', strtotime($transfer->date ?? 'now')) ?>
                                </small>
                            </td>
                            <td>
                                <span class="badge bg-danger">
                                    <i class="fas fa-arrow-left"></i>
                                    <?= htmlspecialchars($transfer->from_account_name ?? 'Неизвестна') ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-success">
                                    <i class="fas fa-arrow-right"></i>
                                    <?= htmlspecialchars($transfer->to_account_name ?? 'Неизвестна') ?>
                                </span>
                            </td>
                            <td>
                                <strong class="text-primary"><?= number_format($transfer->amount ?? 0, 2) ?> лв.</strong>
                            </td>
                            <td><?= htmlspecialchars($transfer->description ?? 'Без описание') ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <div class="text-center">
                <a href="/admin/strixbudget/transfers" class="btn btn-outline-info">
                    <i class="fas fa-arrows-alt-h me-2"></i>Виж всички трансфери
                </a>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Bank Accounts -->
    <?php if (!empty($account_balances)): ?>
    <div class="card mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">🏦 Банкови сметки</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Сметка</th>
                            <th>Валута</th>
                            <th>Баланс</th>
                            <th>Статус</th>
                            <th class="text-center">Действия</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($account_balances as $account): ?>
                        <tr>
                            <td>
                                <strong><?= htmlspecialchars($account->name ?? 'Неизвестна сметка') ?></strong>
                                <?php if (method_exists($account, 'isDefault') && $account->isDefault()): ?>
                                    <br><span class="badge bg-primary">По подразбиране</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">
                                    <?= htmlspecialchars($account->currency ?? 'EUR') ?>
                                </span>
                            </td>
                            <td>
                                <strong class="text-success">
                                    <?= number_format($account->balance ?? 0, 2) ?> <?= htmlspecialchars($account->currency ?? 'EUR') ?>
                                </strong>
                            </td>
                            <td>
                                <?php if (method_exists($account, 'isActive') && $account->isActive()): ?>
                                    <span class="badge bg-success">Активна</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">Неактивна</span>
                                <?php endif; ?>
                            </td>
                            <td class="text-center">
                                <a href="/admin/strixbudget/bank-accounts/<?= $account->id ?>"
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> Преглед
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <div class="text-center">
                <a href="/admin/strixbudget/bank-accounts" class="btn btn-outline-primary">
                    <i class="fas fa-university me-2"></i>Управление на сметки
                </a>
            </div>
        </div>
    </div>
    <?php endif; ?>

<?php endif; ?>

    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
