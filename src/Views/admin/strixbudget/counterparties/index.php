<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="card">
    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
        <h3>🏢 Контрагенти</h3>
        <div style="display: flex; gap: 10px; align-items: center;">
            <a href="/admin/strixbudget/counterparties/create" class="btn btn-primary">
                ➕ Нов контрагент
            </a>
            <a href="/admin/strixbudget" class="btn btn-outline-secondary">
                ← Назад към dashboard
            </a>
        </div>
    </div>
    <div class="card-body">

            <!-- Search and Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">Търсене</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="Име, email, телефон..." 
                                       value="<?= htmlspecialchars($filters['search'] ?? '') ?>">
                                <button class="btn btn-outline-secondary" type="submit">🔍</button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label for="type" class="form-label">Тип</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">Всички типове</option>
                                <option value="client" <?= ($filters['type'] ?? '') === 'client' ? 'selected' : '' ?>>Клиент</option>
                                <option value="supplier" <?= ($filters['type'] ?? '') === 'supplier' ? 'selected' : '' ?>>Доставчик</option>
                                <option value="employee" <?= ($filters['type'] ?? '') === 'employee' ? 'selected' : '' ?>>Служител</option>
                                <option value="other" <?= ($filters['type'] ?? '') === 'other' ? 'selected' : '' ?>>Друго</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Статус</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">Всички</option>
                                <option value="active" <?= ($filters['status'] ?? '') === 'active' ? 'selected' : '' ?>>Активни</option>
                                <option value="inactive" <?= ($filters['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Неактивни</option>
                            </select>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-white bg-primary">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Общо контрагенти</h6>
                                    <h4><?= count($counterparties) ?></h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-success">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Клиенти</h6>
                                    <h4><?= count(array_filter($counterparties, fn($c) => ($c->type ?? '') === 'client')) ?></h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-tie fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-warning">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Доставчици</h6>
                                    <h4><?= count(array_filter($counterparties, fn($c) => ($c->type ?? '') === 'supplier')) ?></h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-truck fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-info">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Служители</h6>
                                    <h4><?= count(array_filter($counterparties, fn($c) => ($c->type ?? '') === 'employee')) ?></h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-friends fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Counterparties Table -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Списък с контрагенти</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($counterparties)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-address-book fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Няма намерени контрагенти</h5>
                            <p class="text-muted">Създайте първия си контрагент или променете филтрите.</p>
                            <a href="/admin/strixbudget/counterparties/create" class="btn btn-primary">
                                ➕ Създаване на контрагент
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Име</th>
                                        <th>Тип</th>
                                        <th>Контакти</th>
                                        <th>Описание</th>
                                        <th>Статус</th>
                                        <th class="text-center">Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($counterparties as $counterparty): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-3">
                                                        <?php
                                                        $typeIcons = [
                                                            'client' => 'fas fa-user-tie text-success',
                                                            'supplier' => 'fas fa-truck text-warning',
                                                            'employee' => 'fas fa-user-friends text-info',
                                                            'other' => 'fas fa-user text-secondary'
                                                        ];
                                                        $iconClass = $typeIcons[$counterparty->type ?? 'other'] ?? $typeIcons['other'];
                                                        ?>
                                                        <i class="<?= $iconClass ?> fa-2x"></i>
                                                    </div>
                                                    <div>
                                                        <strong><?= htmlspecialchars($counterparty->name ?? 'Без име') ?></strong>
                                                        <?php if ($counterparty->company): ?>
                                                            <br><small class="text-muted"><?= htmlspecialchars($counterparty->company) ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php
                                                $typeLabels = [
                                                    'client' => ['Клиент', 'success'],
                                                    'supplier' => ['Доставчик', 'warning'],
                                                    'employee' => ['Служител', 'info'],
                                                    'other' => ['Друго', 'secondary']
                                                ];
                                                $typeInfo = $typeLabels[$counterparty->type ?? 'other'] ?? $typeLabels['other'];
                                                ?>
                                                <span class="badge bg-<?= $typeInfo[1] ?>">
                                                    <?= $typeInfo[0] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($counterparty->email): ?>
                                                    <div><i class="fas fa-envelope text-muted"></i> 
                                                        <a href="mailto:<?= htmlspecialchars($counterparty->email) ?>">
                                                            <?= htmlspecialchars($counterparty->email) ?>
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if ($counterparty->phone): ?>
                                                    <div><i class="fas fa-phone text-muted"></i> 
                                                        <a href="tel:<?= htmlspecialchars($counterparty->phone) ?>">
                                                            <?= htmlspecialchars($counterparty->phone) ?>
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if (!$counterparty->email && !$counterparty->phone): ?>
                                                    <span class="text-muted">Няма контакти</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($counterparty->description): ?>
                                                    <span class="text-truncate" style="max-width: 200px; display: inline-block;" 
                                                          title="<?= htmlspecialchars($counterparty->description) ?>">
                                                        <?= htmlspecialchars($counterparty->description) ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (($counterparty->is_active ?? true)): ?>
                                                    <span class="badge bg-success">Активен</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Неактивен</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group btn-group-sm">
                                                    <a href="/admin/strixbudget/counterparties/<?= $counterparty->id ?>" 
                                                       class="btn btn-outline-info" title="Преглед">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="/admin/strixbudget/counterparties/<?= $counterparty->id ?>/edit" 
                                                       class="btn btn-outline-warning" title="Редактиране">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-outline-danger" 
                                                            title="Изтриване"
                                                            onclick="deleteCounterparty(<?= $counterparty->id ?>)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination would go here -->
                        <?php if (isset($pagination)): ?>
                            <nav aria-label="Pagination">
                                <ul class="pagination justify-content-center">
                                    <!-- Pagination links -->
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
    </div>
</div>

<script>
function deleteCounterparty(id) {
    if (confirm('Сигурни ли сте, че искате да изтриете този контрагент?')) {
        fetch(`/admin/strixbudget/counterparties/${id}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Грешка при изтриване: ' + data.message);
            }
        })
        .catch(error => {
            alert('Грешка при заявката: ' + error.message);
        });
    }
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../../layouts/app.php';
?>
