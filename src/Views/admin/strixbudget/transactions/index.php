<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="card">
    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
        <h3>💳 Транзакции</h3>
        <div style="display: flex; gap: 10px; align-items: center;">
            <a href="/admin/strixbudget/transactions/create" class="btn btn-primary">
                ➕ Нова транзакция
            </a>
            <a href="/admin/strixbudget" class="btn btn-outline-secondary">
                ← Назад към dashboard
            </a>
        </div>
    </div>
    <div class="card-body">

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="bank_account_id" class="form-label">Банкова сметка</label>
                            <select class="form-select" id="bank_account_id" name="bank_account_id">
                                <option value="">Всички сметки</option>
                                <?php foreach ($bankAccounts as $account): ?>
                                    <option value="<?= $account->id ?>" 
                                            <?= ($filters['bank_account_id'] ?? '') == $account->id ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($account->name) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="type" class="form-label">Тип</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">Всички типове</option>
                                <option value="income" <?= ($filters['type'] ?? '') === 'income' ? 'selected' : '' ?>>Приход</option>
                                <option value="expense" <?= ($filters['type'] ?? '') === 'expense' ? 'selected' : '' ?>>Разход</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">От дата</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?= htmlspecialchars($filters['date_from'] ?? '') ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">До дата</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?= htmlspecialchars($filters['date_to'] ?? '') ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="search" class="form-label">Търсене</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="Описание, контрагент..." 
                                       value="<?= htmlspecialchars($filters['search'] ?? '') ?>">
                                <button class="btn btn-outline-secondary" type="submit">🔍</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-white bg-success">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Общо приходи</h6>
                                    <h4><?= number_format($stats['total_income'] ?? 0, 2) ?> лв.</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-arrow-up fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-danger">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Общо разходи</h6>
                                    <h4><?= number_format($stats['total_expense'] ?? 0, 2) ?> лв.</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-arrow-down fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-info">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Нетен резултат</h6>
                                    <h4><?= number_format(($stats['total_income'] ?? 0) - ($stats['total_expense'] ?? 0), 2) ?> лв.</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-balance-scale fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-secondary">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Брой транзакции</h6>
                                    <h4><?= count($transactions) ?></h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-list fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transactions Table -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Списък с транзакции</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($transactions)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Няма намерени транзакции</h5>
                            <p class="text-muted">Създайте първата си транзакция или променете филтрите.</p>
                            <a href="/admin/strixbudget/transactions/create" class="btn btn-primary">
                                ➕ Създаване на транзакция
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Дата</th>
                                        <th>Описание</th>
                                        <th>Банкова сметка</th>
                                        <th>Контрагент</th>
                                        <th>Тип</th>
                                        <th class="text-end">Сума</th>
                                        <th class="text-center">Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($transactions as $transaction): ?>
                                        <tr>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('d.m.Y', strtotime($transaction->date ?? 'now')) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <strong><?= htmlspecialchars($transaction->description ?? 'Без описание') ?></strong>
                                                <?php if ($transaction->reference): ?>
                                                    <br><small class="text-muted">Ref: <?= htmlspecialchars($transaction->reference) ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?= htmlspecialchars($transaction->bank_account_name ?? 'Неизвестна сметка') ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($transaction->counterparty_name): ?>
                                                    <?= htmlspecialchars($transaction->counterparty_name) ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($transaction->isIncome()): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-arrow-up"></i> Приход
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-arrow-down"></i> Разход
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-end">
                                                <strong class="<?= $transaction->isIncome() ? 'text-success' : 'text-danger' ?>">
                                                    <?= $transaction->getFormattedAmount() ?>
                                                </strong>
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group btn-group-sm">
                                                    <a href="/admin/strixbudget/transactions/<?= $transaction->id ?>" 
                                                       class="btn btn-outline-info" title="Преглед">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="/admin/strixbudget/transactions/<?= $transaction->id ?>/edit" 
                                                       class="btn btn-outline-warning" title="Редактиране">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-outline-danger" 
                                                            title="Изтриване"
                                                            onclick="deleteTransaction(<?= $transaction->id ?>)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination would go here -->
                        <?php if (isset($pagination)): ?>
                            <nav aria-label="Pagination">
                                <ul class="pagination justify-content-center">
                                    <!-- Pagination links -->
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
    </div>
</div>

<script>
function deleteTransaction(id) {
    if (confirm('Сигурни ли сте, че искате да изтриете тази транзакция?')) {
        fetch(`/admin/strixbudget/transactions/${id}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Грешка при изтриване: ' + data.message);
            }
        })
        .catch(error => {
            alert('Грешка при заявката: ' + error.message);
        });
    }
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../../layouts/app.php';
?>
