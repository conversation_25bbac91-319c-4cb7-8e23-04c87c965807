<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="card">
    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
        <h3>➕ Създаване на транзакция</h3>
        <div style="display: flex; gap: 10px; align-items: center;">
            <a href="/admin/strixbudget/transactions" class="btn btn-outline-secondary">
                ← Назад към списъка
            </a>
        </div>
    </div>
    <div class="card-body">

            <?php if (isset($error)): ?>
                <div class="alert alert-danger" role="alert">
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <?php if (isset($errors) && !empty($errors)): ?>
                <div class="alert alert-danger" role="alert">
                    <ul class="mb-0">
                        <?php foreach ($errors as $field => $error): ?>
                            <li><?= htmlspecialchars(is_array($error) ? implode(', ', $error) : $error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Данни за транзакцията</h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="/admin/strixbudget/transactions">
                                <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="bank_account_id" class="form-label">Банкова сметка *</label>
                                            <select class="form-select <?= isset($errors['bank_account_id']) ? 'is-invalid' : '' ?>" 
                                                    id="bank_account_id" 
                                                    name="bank_account_id" 
                                                    required>
                                                <option value="">Изберете банкова сметка</option>
                                                <?php foreach ($bankAccounts as $account): ?>
                                                    <option value="<?= $account->id ?>" 
                                                            <?= ($old_input['bank_account_id'] ?? '') == $account->id ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($account->name) ?> (<?= htmlspecialchars($account->currency) ?>)
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <?php if (isset($errors['bank_account_id'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['bank_account_id']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="type" class="form-label">Тип транзакция *</label>
                                            <select class="form-select <?= isset($errors['type']) ? 'is-invalid' : '' ?>" 
                                                    id="type" 
                                                    name="type" 
                                                    required>
                                                <option value="">Изберете тип</option>
                                                <option value="income" <?= ($old_input['type'] ?? '') === 'income' ? 'selected' : '' ?>>
                                                    💰 Приход
                                                </option>
                                                <option value="expense" <?= ($old_input['type'] ?? '') === 'expense' ? 'selected' : '' ?>>
                                                    💸 Разход
                                                </option>
                                            </select>
                                            <?php if (isset($errors['type'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['type']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="amount" class="form-label">Сума *</label>
                                            <div class="input-group">
                                                <input type="number" 
                                                       class="form-control <?= isset($errors['amount']) ? 'is-invalid' : '' ?>" 
                                                       id="amount" 
                                                       name="amount" 
                                                       value="<?= htmlspecialchars($old_input['amount'] ?? '') ?>"
                                                       step="0.01"
                                                       min="0.01"
                                                       placeholder="0.00"
                                                       required>
                                                <span class="input-group-text" id="currency-symbol">лв.</span>
                                            </div>
                                            <?php if (isset($errors['amount'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['amount']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="date" class="form-label">Дата *</label>
                                            <input type="date" 
                                                   class="form-control <?= isset($errors['date']) ? 'is-invalid' : '' ?>" 
                                                   id="date" 
                                                   name="date" 
                                                   value="<?= htmlspecialchars($old_input['date'] ?? date('Y-m-d')) ?>"
                                                   required>
                                            <?php if (isset($errors['date'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['date']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="description" class="form-label">Описание *</label>
                                    <input type="text" 
                                           class="form-control <?= isset($errors['description']) ? 'is-invalid' : '' ?>" 
                                           id="description" 
                                           name="description" 
                                           value="<?= htmlspecialchars($old_input['description'] ?? '') ?>"
                                           placeholder="Например: Заплата, Покупка на храна, Плащане на сметка"
                                           required>
                                    <div class="form-text">Въведете кратко описание на транзакцията</div>
                                    <?php if (isset($errors['description'])): ?>
                                        <div class="invalid-feedback">
                                            <?= htmlspecialchars($errors['description']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="counterparty_id" class="form-label">Контрагент</label>
                                            <select class="form-select <?= isset($errors['counterparty_id']) ? 'is-invalid' : '' ?>" 
                                                    id="counterparty_id" 
                                                    name="counterparty_id">
                                                <option value="">Без контрагент</option>
                                                <?php foreach ($counterparties as $counterparty): ?>
                                                    <option value="<?= $counterparty->id ?>" 
                                                            <?= ($old_input['counterparty_id'] ?? '') == $counterparty->id ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($counterparty->name) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="form-text">Изберете контрагент или оставете празно</div>
                                            <?php if (isset($errors['counterparty_id'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['counterparty_id']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="transaction_type_id" class="form-label">Категория</label>
                                            <select class="form-select <?= isset($errors['transaction_type_id']) ? 'is-invalid' : '' ?>" 
                                                    id="transaction_type_id" 
                                                    name="transaction_type_id">
                                                <option value="">Без категория</option>
                                                <?php foreach ($transactionTypes as $type): ?>
                                                    <option value="<?= $type->id ?>" 
                                                            <?= ($old_input['transaction_type_id'] ?? '') == $type->id ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($type->name) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="form-text">Изберете категория за по-добра организация</div>
                                            <?php if (isset($errors['transaction_type_id'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['transaction_type_id']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="reference" class="form-label">Референция</label>
                                    <input type="text" 
                                           class="form-control <?= isset($errors['reference']) ? 'is-invalid' : '' ?>" 
                                           id="reference" 
                                           name="reference" 
                                           value="<?= htmlspecialchars($old_input['reference'] ?? '') ?>"
                                           placeholder="Номер на фактура, чек, банков код и т.н.">
                                    <div class="form-text">Опционално поле за референтен номер или код</div>
                                    <?php if (isset($errors['reference'])): ?>
                                        <div class="invalid-feedback">
                                            <?= htmlspecialchars($errors['reference']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="notes" class="form-label">Бележки</label>
                                    <textarea class="form-control <?= isset($errors['notes']) ? 'is-invalid' : '' ?>" 
                                              id="notes" 
                                              name="notes" 
                                              rows="3"
                                              placeholder="Допълнителни бележки за транзакцията..."><?= htmlspecialchars($old_input['notes'] ?? '') ?></textarea>
                                    <div class="form-text">Опционално поле за допълнителна информация</div>
                                    <?php if (isset($errors['notes'])): ?>
                                        <div class="invalid-feedback">
                                            <?= htmlspecialchars($errors['notes']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="/admin/strixbudget/transactions" class="btn btn-secondary me-md-2">
                                        Отказ
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        💾 Създаване на транзакция
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-info">Помощ</h6>
                        </div>
                        <div class="card-body">
                            <h6>Съвети за създаване на транзакция:</h6>
                            <ul class="small">
                                <li><strong>Приход</strong> - увеличава баланса на сметката</li>
                                <li><strong>Разход</strong> - намалява баланса на сметката</li>
                                <li>Използвайте описателни имена</li>
                                <li>Добавете контрагент за по-добро проследяване</li>
                                <li>Категориите помагат за анализ на разходите</li>
                            </ul>

                            <hr>

                            <h6>Бързи действия:</h6>
                            <div class="d-grid gap-2">
                                <a href="/admin/strixbudget/counterparties/create" class="btn btn-sm btn-outline-primary">
                                    ➕ Нов контрагент
                                </a>
                                <a href="/admin/strixbudget/transaction-types/create" class="btn btn-sm btn-outline-secondary">
                                    ➕ Нова категория
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Balance Info -->
                    <div class="card shadow mt-3">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-success">Баланси на сметки</h6>
                        </div>
                        <div class="card-body">
                            <?php foreach ($bankAccounts as $account): ?>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="small"><?= htmlspecialchars($account->name) ?></span>
                                    <span class="small font-weight-bold">
                                        <?= number_format($account->balance ?? 0, 2) ?> <?= htmlspecialchars($account->currency) ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update currency symbol when bank account changes
    const bankAccountSelect = document.getElementById('bank_account_id');
    const currencySymbol = document.getElementById('currency-symbol');
    
    if (bankAccountSelect && currencySymbol) {
        bankAccountSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                // Extract currency from option text (assumes format: "Name (CURRENCY)")
                const match = selectedOption.text.match(/\(([^)]+)\)$/);
                if (match) {
                    currencySymbol.textContent = match[1];
                }
            } else {
                currencySymbol.textContent = 'лв.';
            }
        });
    }

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const bankAccount = document.getElementById('bank_account_id').value;
            const type = document.getElementById('type').value;
            const amount = document.getElementById('amount').value;
            const description = document.getElementById('description').value.trim();

            if (!bankAccount) {
                alert('Моля изберете банкова сметка');
                e.preventDefault();
                return;
            }

            if (!type) {
                alert('Моля изберете тип транзакция');
                e.preventDefault();
                return;
            }

            if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
                alert('Моля въведете валидна сума');
                e.preventDefault();
                return;
            }

            if (!description) {
                alert('Моля въведете описание');
                e.preventDefault();
                return;
            }
        });
    }
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../../layouts/app.php';
?>
