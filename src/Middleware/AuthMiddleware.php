<?php

namespace Strix\ERP\Middleware;

use Strix\ERP\Core\Application;

class AuthMiddleware
{
    public function handle(): void
    {
        $app = Application::getInstance();
        
        // Check if user is logged in
        if (!$app->isLoggedIn()) {
            // Store intended URL for redirect after login
            $_SESSION['intended_url'] = $_SERVER['REQUEST_URI'];
            
            // Redirect to login page
            $app->redirect('/login');
        }
    }
}
