<?php

namespace Strix\ERP\Middleware;

use Strix\ERP\Core\Application;

class PermissionMiddleware
{
    private string $requiredPermission;

    public function __construct(string $permission)
    {
        $this->requiredPermission = $permission;
    }

    public function handle(): void
    {
        $app = Application::getInstance();
        
        // Check if user is logged in
        if (!$app->isLoggedIn()) {
            $_SESSION['intended_url'] = $_SERVER['REQUEST_URI'];
            $app->redirect('/login');
        }
        
        // Check if user has the required permission
        if (!$app->hasPermission($this->requiredPermission)) {
            $app->setFlashMessage('error', 'Нямате права за достъп до тази страница');
            $app->redirect('/admin');
        }
    }

    public static function create(string $permission): self
    {
        return new self($permission);
    }
}
