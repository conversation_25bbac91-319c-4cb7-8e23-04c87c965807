<?php

namespace Strix\ERP\Core;

use Strix\ERP\Models\User;
use Strix\ERP\Models\Group;
use Strix\ERP\Models\Permission;

class PermissionManager
{
    private static ?self $instance = null;

    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        
        return self::$instance;
    }

    /**
     * Check if a user has a specific permission
     */
    public function userHasPermission(int $userId, string $permission): bool
    {
        $user = User::find($userId);
        if (!$user) {
            return false;
        }

        return $user->hasPermission($permission);
    }

    /**
     * Check if a group has a specific permission
     */
    public function groupHasPermission(int $groupId, string $permission): bool
    {
        $group = Group::find($groupId);
        if (!$group) {
            return false;
        }

        return $group->hasPermission($permission);
    }

    /**
     * Get all permissions for a user
     */
    public function getUserPermissions(int $userId): array
    {
        $user = User::find($userId);
        if (!$user) {
            return [];
        }

        return $user->getPermissionNames();
    }

    /**
     * Get all permissions for a group
     */
    public function getGroupPermissions(int $groupId): array
    {
        $group = Group::find($groupId);
        if (!$group) {
            return [];
        }

        return $group->getPermissionNames();
    }

    /**
     * Check if user has any of the given permissions
     */
    public function userHasAnyPermission(int $userId, array $permissions): bool
    {
        $userPermissions = $this->getUserPermissions($userId);
        
        foreach ($permissions as $permission) {
            if (in_array($permission, $userPermissions)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if user has all of the given permissions
     */
    public function userHasAllPermissions(int $userId, array $permissions): bool
    {
        $userPermissions = $this->getUserPermissions($userId);
        
        foreach ($permissions as $permission) {
            if (!in_array($permission, $userPermissions)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Get permissions grouped by module
     */
    public function getPermissionsByModule(): array
    {
        return Permission::getGroupedByModule();
    }

    /**
     * Get all available modules
     */
    public function getModules(): array
    {
        return Permission::getAllModules();
    }

    /**
     * Create a new permission
     */
    public function createPermission(string $name, string $description, string $module, string $action): ?Permission
    {
        try {
            $permission = new Permission();
            $permission->name = $name;
            $permission->description = $description;
            $permission->module = $module;
            $permission->action = $action;
            
            if ($permission->save()) {
                return $permission;
            }
        } catch (\Exception $e) {
            // Log error
            error_log("Failed to create permission: " . $e->getMessage());
        }
        
        return null;
    }

    /**
     * Grant permission to group
     */
    public function grantPermissionToGroup(int $groupId, int $permissionId, ?int $grantedBy = null): bool
    {
        $group = Group::find($groupId);
        if (!$group) {
            return false;
        }

        return $group->addPermission($permissionId, $grantedBy);
    }

    /**
     * Revoke permission from group
     */
    public function revokePermissionFromGroup(int $groupId, int $permissionId): bool
    {
        $group = Group::find($groupId);
        if (!$group) {
            return false;
        }

        return $group->removePermission($permissionId);
    }

    /**
     * Add user to group
     */
    public function addUserToGroup(int $userId, int $groupId, ?int $assignedBy = null): bool
    {
        $user = User::find($userId);
        if (!$user) {
            return false;
        }

        return $user->addToGroup($groupId, $assignedBy);
    }

    /**
     * Remove user from group
     */
    public function removeUserFromGroup(int $userId, int $groupId): bool
    {
        $user = User::find($userId);
        if (!$user) {
            return false;
        }

        return $user->removeFromGroup($groupId);
    }

    /**
     * Check if current session user has permission
     */
    public function currentUserHasPermission(string $permission): bool
    {
        $app = Application::getInstance();
        return $app->hasPermission($permission);
    }

    /**
     * Get current user's permissions
     */
    public function getCurrentUserPermissions(): array
    {
        $app = Application::getInstance();
        $user = $app->getCurrentUser();
        
        return $user['permissions'] ?? [];
    }

    /**
     * Check if permission exists
     */
    public function permissionExists(string $permission): bool
    {
        return Permission::findByName($permission) !== null;
    }

    /**
     * Get permission by name
     */
    public function getPermissionByName(string $name): ?Permission
    {
        return Permission::findByName($name);
    }

    /**
     * Validate permission format
     */
    public function isValidPermissionFormat(string $permission): bool
    {
        // Permission should be in format: module.action
        return preg_match('/^[a-z_]+\.[a-z_]+$/', $permission) === 1;
    }

    /**
     * Get users with specific permission
     */
    public function getUsersWithPermission(string $permission): array
    {
        $permissionObj = Permission::findByName($permission);
        if (!$permissionObj) {
            return [];
        }

        return $permissionObj->getUsers();
    }

    /**
     * Get groups with specific permission
     */
    public function getGroupsWithPermission(string $permission): array
    {
        $permissionObj = Permission::findByName($permission);
        if (!$permissionObj) {
            return [];
        }

        return $permissionObj->getGroups();
    }
}
