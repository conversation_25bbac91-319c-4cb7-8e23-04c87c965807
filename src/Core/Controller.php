<?php

namespace Strix\ERP\Core;

abstract class Controller
{
    protected Application $app;

    public function __construct()
    {
        $this->app = Application::getInstance();
    }

    protected function view(string $template, array $data = []): void
    {
        $viewPath = __DIR__ . '/../Views/' . $template . '.php';
        
        if (!file_exists($viewPath)) {
            throw new \RuntimeException("View {$template} not found");
        }
        
        // Extract data to variables
        extract($data);
        
        // Start output buffering
        ob_start();
        
        // Include the view file
        include $viewPath;
        
        // Get the content and clean the buffer
        $content = ob_get_clean();
        
        // If this is an AJAX request, return JSON
        if ($this->isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'html' => $content,
                'data' => $data
            ]);
            return;
        }
        
        echo $content;
    }

    protected function json(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
    }

    protected function redirect(string $url): void
    {
        $this->app->redirect($url);
    }

    protected function redirectWithMessage(string $url, string $type, string $message): void
    {
        $this->app->setFlashMessage($type, $message);
        $this->redirect($url);
    }

    protected function isAjaxRequest(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    protected function validateCsrfToken(): bool
    {
        $token = $_POST['_token'] ?? $_GET['_token'] ?? '';
        return hash_equals($_SESSION['csrf_token'] ?? '', $token);
    }

    protected function generateCsrfToken(): string
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    protected function requireAuth(): void
    {
        if (!$this->app->isLoggedIn()) {
            if ($this->isAjaxRequest()) {
                $this->json(['error' => 'Необходима е автентикация'], 401);
                exit;
            }
            $this->redirect('/login');
        }
    }

    protected function requirePermission(string $permission): void
    {
        $this->requireAuth();
        
        if (!$this->app->hasPermission($permission)) {
            if ($this->isAjaxRequest()) {
                $this->json(['error' => 'Нямате права за това действие'], 403);
                exit;
            }
            
            $this->app->setFlashMessage('error', 'Нямате права за това действие');
            $this->redirect('/admin');
        }
    }

    protected function getInput(string $key, mixed $default = null): mixed
    {
        return $_POST[$key] ?? $_GET[$key] ?? $default;
    }

    protected function getAllInput(): array
    {
        return array_merge($_GET, $_POST);
    }

    protected function validate(array $rules): array
    {
        $errors = [];
        $data = $this->getAllInput();
        
        foreach ($rules as $field => $fieldRules) {
            $value = $data[$field] ?? null;
            $fieldRules = is_string($fieldRules) ? explode('|', $fieldRules) : $fieldRules;
            
            foreach ($fieldRules as $rule) {
                $error = $this->validateField($field, $value, $rule);
                if ($error) {
                    $errors[$field][] = $error;
                }
            }
        }
        
        return $errors;
    }

    private function validateField(string $field, mixed $value, string $rule): ?string
    {
        [$ruleName, $parameter] = explode(':', $rule . ':');
        
        switch ($ruleName) {
            case 'required':
                if (empty($value)) {
                    return "Полето {$field} е задължително";
                }
                break;
                
            case 'email':
                if ($value && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    return "Полето {$field} трябва да е валиден имейл адрес";
                }
                break;
                
            case 'min':
                if ($value && strlen($value) < (int)$parameter) {
                    return "Полето {$field} трябва да е поне {$parameter} символа";
                }
                break;
                
            case 'max':
                if ($value && strlen($value) > (int)$parameter) {
                    return "Полето {$field} не може да е повече от {$parameter} символа";
                }
                break;
                
            case 'unique':
                [$table, $column] = explode(',', $parameter . ',id');
                $existing = Database::fetchOne(
                    "SELECT id FROM {$table} WHERE {$column} = ?",
                    [$value]
                );
                if ($existing) {
                    return "Полето {$field} вече съществува";
                }
                break;
        }
        
        return null;
    }
}
