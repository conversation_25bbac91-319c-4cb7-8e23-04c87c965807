<?php

namespace Strix\ERP\Core;

class ViewHelpers
{
    /**
     * Check if current user has permission
     */
    public static function hasPermission(string $permission): bool
    {
        $app = Application::getInstance();
        return $app->hasPermission($permission);
    }

    /**
     * Check if current user has any of the given permissions
     */
    public static function hasAnyPermission(array $permissions): bool
    {
        $app = Application::getInstance();
        $userPermissions = $app->getCurrentUser()['permissions'] ?? [];
        
        foreach ($permissions as $permission) {
            if (in_array($permission, $userPermissions)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if current user has all of the given permissions
     */
    public static function hasAllPermissions(array $permissions): bool
    {
        $app = Application::getInstance();
        $userPermissions = $app->getCurrentUser()['permissions'] ?? [];
        
        foreach ($permissions as $permission) {
            if (!in_array($permission, $userPermissions)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Get current user data
     */
    public static function getCurrentUser(): ?array
    {
        $app = Application::getInstance();
        return $app->getCurrentUser();
    }

    /**
     * Check if user is logged in
     */
    public static function isLoggedIn(): bool
    {
        $app = Application::getInstance();
        return $app->isLoggedIn();
    }

    /**
     * Check if current user is admin
     */
    public static function isAdmin(): bool
    {
        $user = self::getCurrentUser();
        return $user['is_admin'] ?? false;
    }

    /**
     * Escape HTML
     */
    public static function escape(string $text): string
    {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Format date
     */
    public static function formatDate(string $date, string $format = 'd.m.Y'): string
    {
        return date($format, strtotime($date));
    }

    /**
     * Format datetime
     */
    public static function formatDateTime(string $datetime, string $format = 'd.m.Y H:i'): string
    {
        return date($format, strtotime($datetime));
    }

    /**
     * Generate CSRF token
     */
    public static function csrfToken(): string
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    /**
     * Get flash messages
     */
    public static function getFlashMessages(): array
    {
        $app = Application::getInstance();
        return $app->getFlashMessages();
    }

    /**
     * Check if route is active
     */
    public static function isActiveRoute(string $route): bool
    {
        $currentPath = $_SERVER['REQUEST_URI'];
        return strpos($currentPath, $route) === 0;
    }

    /**
     * Generate URL
     */
    public static function url(string $path): string
    {
        $app = Application::getInstance();
        $baseUrl = $app->getConfig('url') ?? '';
        return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
    }

    /**
     * Get old input value
     */
    public static function old(string $key, mixed $default = ''): mixed
    {
        return $_SESSION['old_input'][$key] ?? $default;
    }

    /**
     * Store old input
     */
    public static function storeOldInput(array $input): void
    {
        $_SESSION['old_input'] = $input;
    }

    /**
     * Clear old input
     */
    public static function clearOldInput(): void
    {
        unset($_SESSION['old_input']);
    }

    /**
     * Format file size
     */
    public static function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Truncate text
     */
    public static function truncate(string $text, int $length = 100, string $suffix = '...'): string
    {
        if (strlen($text) <= $length) {
            return $text;
        }
        
        return substr($text, 0, $length) . $suffix;
    }

    /**
     * Generate pagination links
     */
    public static function paginate(int $currentPage, int $totalPages, string $baseUrl, array $params = []): string
    {
        if ($totalPages <= 1) {
            return '';
        }
        
        $html = '<div class="pagination">';
        
        // Previous page
        if ($currentPage > 1) {
            $prevUrl = $baseUrl . '?page=' . ($currentPage - 1);
            if (!empty($params)) {
                $prevUrl .= '&' . http_build_query($params);
            }
            $html .= '<a href="' . $prevUrl . '" class="btn btn-warning btn-sm">« Предишна</a>';
        }
        
        // Page numbers
        $start = max(1, $currentPage - 2);
        $end = min($totalPages, $currentPage + 2);
        
        for ($i = $start; $i <= $end; $i++) {
            $pageUrl = $baseUrl . '?page=' . $i;
            if (!empty($params)) {
                $pageUrl .= '&' . http_build_query($params);
            }
            
            $class = $i === $currentPage ? 'btn-primary' : 'btn-warning';
            $html .= '<a href="' . $pageUrl . '" class="btn ' . $class . ' btn-sm">' . $i . '</a>';
        }
        
        // Next page
        if ($currentPage < $totalPages) {
            $nextUrl = $baseUrl . '?page=' . ($currentPage + 1);
            if (!empty($params)) {
                $nextUrl .= '&' . http_build_query($params);
            }
            $html .= '<a href="' . $nextUrl . '" class="btn btn-warning btn-sm">Следваща »</a>';
        }
        
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Generate breadcrumbs
     */
    public static function breadcrumbs(array $items): string
    {
        if (empty($items)) {
            return '';
        }
        
        $html = '<nav class="breadcrumbs"><ol>';
        
        foreach ($items as $item) {
            if (isset($item['url'])) {
                $html .= '<li><a href="' . $item['url'] . '">' . self::escape($item['title']) . '</a></li>';
            } else {
                $html .= '<li class="active">' . self::escape($item['title']) . '</li>';
            }
        }
        
        $html .= '</ol></nav>';
        
        return $html;
    }
}
