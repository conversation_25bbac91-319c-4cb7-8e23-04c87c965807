<?php

namespace Strix\ERP\Core;

abstract class Model
{
    protected string $table;
    protected string $primaryKey = 'id';
    protected array $fillable = [];
    protected array $hidden = [];
    protected array $casts = [];
    protected array $attributes = [];

    public function __construct()
    {
        if (empty($this->table)) {
            $className = (new \ReflectionClass($this))->getShortName();
            $this->table = strtolower($className) . 's';
        }
    }

    public static function find(int $id): ?static
    {
        $instance = new static();
        $data = Database::fetchOne(
            "SELECT * FROM {$instance->table} WHERE {$instance->primaryKey} = ?",
            [$id]
        );
        
        if (!$data) {
            return null;
        }

        foreach ($data as $key => $value) {
            $instance->attributes[$key] = $value;
        }

        return $instance;
    }

    public static function findBy(string $column, mixed $value): ?static
    {
        $instance = new static();
        $data = Database::fetchOne(
            "SELECT * FROM {$instance->table} WHERE {$column} = ?",
            [$value]
        );
        
        if (!$data) {
            return null;
        }

        foreach ($data as $key => $value) {
            $instance->attributes[$key] = $value;
        }

        return $instance;
    }

    public static function all(): array
    {
        $instance = new static();
        $results = Database::fetchAll("SELECT * FROM {$instance->table}");
        
        $instances = [];
        foreach ($results as $data) {
            $newInstance = new static();
            foreach ($data as $key => $value) {
                $newInstance->attributes[$key] = $value;
            }
            $instances[] = $newInstance;
        }
        return $instances;
    }

    public static function where(string $column, mixed $value): array
    {
        $instance = new static();
        $results = Database::fetchAll(
            "SELECT * FROM {$instance->table} WHERE {$column} = ?",
            [$value]
        );
        
        $instances = [];
        foreach ($results as $data) {
            $newInstance = new static();
            foreach ($data as $key => $value) {
                $newInstance->attributes[$key] = $value;
            }
            $instances[] = $newInstance;
        }
        return $instances;
    }

    public static function count(array $conditions = []): int
    {
        $instance = new static();

        $whereClause = '';
        $params = [];

        if (!empty($conditions)) {
            $whereParts = [];
            foreach ($conditions as $column => $value) {
                $whereParts[] = "$column = ?";
                $params[] = $value;
            }
            $whereClause = "WHERE " . implode(" AND ", $whereParts);
        }

        return (int) Database::fetchColumn(
            "SELECT COUNT(*) FROM {$instance->table} $whereClause",
            $params
        );
    }

    /**
     * Get paginated results
     */
    public static function paginate(int $page = 1, int $perPage = 20, array $conditions = [], string $orderBy = 'id DESC'): array
    {
        $instance = new static();
        $offset = ($page - 1) * $perPage;

        $whereClause = '';
        $params = [];

        if (!empty($conditions)) {
            $whereParts = [];
            foreach ($conditions as $column => $value) {
                $whereParts[] = "$column = ?";
                $params[] = $value;
            }
            $whereClause = "WHERE " . implode(" AND ", $whereParts);
        }

        $results = Database::fetchAll(
            "SELECT * FROM {$instance->table} $whereClause ORDER BY $orderBy LIMIT $perPage OFFSET $offset",
            $params
        );

        $instances = [];
        foreach ($results as $data) {
            $newInstance = new static();
            foreach ($data as $key => $value) {
                $newInstance->attributes[$key] = $value;
            }
            $instances[] = $newInstance;
        }

        return $instances;
    }

    /**
     * Get pagination info
     */
    public static function getPaginationInfo(int $page = 1, int $perPage = 20, array $conditions = []): array
    {
        $totalCount = static::count($conditions);
        $totalPages = ceil($totalCount / $perPage);

        return [
            'currentPage' => $page,
            'perPage' => $perPage,
            'totalCount' => $totalCount,
            'totalPages' => $totalPages,
            'hasNextPage' => $page < $totalPages,
            'hasPrevPage' => $page > 1
        ];
    }

    public function save(): bool
    {
        // Call beforeSave hook if it exists
        if (method_exists($this, 'beforeSave')) {
            $this->beforeSave();
        }

        $data = $this->getAttributes();

        if (isset($data[$this->primaryKey])) {
            // Update existing record
            $id = $data[$this->primaryKey];
            unset($data[$this->primaryKey]);

            if (isset($data['updated_at'])) {
                $data['updated_at'] = date('Y-m-d H:i:s');
            }

            return Database::update($this->table, $data, [$this->primaryKey => $id]) > 0;
        } else {
            // Insert new record
            if (isset($data['created_at'])) {
                $data['created_at'] = date('Y-m-d H:i:s');
            }
            if (isset($data['updated_at'])) {
                $data['updated_at'] = date('Y-m-d H:i:s');
            }

            $id = Database::insert($this->table, $data);
            $this->{$this->primaryKey} = $id;
            return $id > 0;
        }
    }

    public function delete(): bool
    {
        if (!isset($this->{$this->primaryKey})) {
            return false;
        }
        
        return Database::delete($this->table, [$this->primaryKey => $this->{$this->primaryKey}]) > 0;
    }

    public static function create(array $data): static
    {
        $instance = new static();
        $instance->fill($data);
        $instance->save();
        return $instance;
    }

    public function fill(array $data): void
    {
        foreach ($data as $key => $value) {
            if (empty($this->fillable) || in_array($key, $this->fillable)) {
                $this->$key = $value;
            }
        }
    }

    /**
     * Hook called before saving the model
     */
    protected function beforeSave(): void
    {
        // Override in child classes if needed
    }

    public function toArray(): array
    {
        $data = $this->getAttributes();
        
        // Remove hidden attributes
        foreach ($this->hidden as $hidden) {
            unset($data[$hidden]);
        }
        
        // Apply casts
        foreach ($this->casts as $key => $cast) {
            if (isset($data[$key])) {
                $data[$key] = $this->castAttribute($data[$key], $cast);
            }
        }
        
        return $data;
    }

    public function toJson(): string
    {
        return json_encode($this->toArray());
    }



    private function getAttributes(): array
    {
        return $this->attributes;
    }

    private function castAttribute(mixed $value, string $cast): mixed
    {
        switch ($cast) {
            case 'int':
            case 'integer':
                return (int) $value;
            case 'float':
            case 'double':
                return (float) $value;
            case 'bool':
            case 'boolean':
                return (bool) $value;
            case 'array':
                return is_string($value) ? json_decode($value, true) : $value;
            case 'json':
                return is_string($value) ? json_decode($value) : $value;
            case 'datetime':
                return new \DateTime($value);
            default:
                return $value;
        }
    }

    public function __get(string $name): mixed
    {
        return $this->attributes[$name] ?? null;
    }

    public function __set(string $name, mixed $value): void
    {
        $this->attributes[$name] = $value;
    }

    public function __isset(string $name): bool
    {
        return isset($this->attributes[$name]);
    }

    public function __unset(string $name): void
    {
        unset($this->attributes[$name]);
    }
}
