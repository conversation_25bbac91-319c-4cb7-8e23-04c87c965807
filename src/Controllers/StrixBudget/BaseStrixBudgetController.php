<?php

namespace Strix\ERP\Controllers\StrixBudget;

use Strix\ERP\Core\Controller;
use Strix\ERP\Services\StrixBudgetClient;
use Strix\ERP\Models\UserStrixBudgetSettings;
use Strix\ERP\Models\StrixBudget\BaseStrixBudgetModel;

abstract class BaseStrixBudgetController extends Controller
{
    protected ?StrixBudgetClient $client = null;
    protected bool $clientInitialized = false;
    
    public function __construct()
    {
        parent::__construct();
    }
    
    /**
     * Initialize StrixBudget client
     */
    protected function initializeClient(): bool
    {
        if ($this->clientInitialized) {
            return $this->client !== null;
        }
        
        $this->requireAuth();
        
        try {
            $currentUser = $this->app->getCurrentUser();
            $settings = UserStrixBudgetSettings::getByUserId($currentUser['id']);
            
            if ($settings && $settings->isConfigured()) {
                $config = $settings->getClientConfig();
                $this->client = new StrixBudgetClient($config['api_url']);

                // Authenticate based on method
                $authSuccess = false;
                if ($settings->usesCredentials()) {
                    try {
                        $loginResult = $this->client->loginAndGetToken($config['api_email'], $config['api_password']);
                        if ($loginResult['success']) {
                            $this->client->setToken($loginResult['token']);
                            $authSuccess = true;
                        } else {
                            error_log("StrixBudget API login failed: " . $loginResult['message']);
                        }
                    } catch (\Exception $e) {
                        error_log("StrixBudget API login exception: " . $e->getMessage());
                    }
                } else {
                    if (!empty($config['api_token'])) {
                        $this->client->setToken($config['api_token']);
                        $authSuccess = true;
                    } else {
                        error_log("StrixBudget API token is empty");
                    }
                }

                if ($authSuccess) {
                    BaseStrixBudgetModel::setClient($this->client);
                } else {
                    $this->client = null;
                    error_log("StrixBudget authentication failed");
                }
            } else {
                error_log("StrixBudget settings not configured for user " . $currentUser['id']);
            }
        } catch (\Exception $e) {
            error_log("StrixBudget client initialization failed: " . $e->getMessage());
            $this->client = null;
        }
        
        $this->clientInitialized = true;
        return $this->client !== null;
    }
    
    /**
     * Ensure client is initialized and redirect to settings if not
     */
    protected function requireStrixBudgetClient(): void
    {
        if (!$this->initializeClient()) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
    }
    
    /**
     * Get the initialized client
     */
    protected function getClient(): ?StrixBudgetClient
    {
        $this->initializeClient();
        return $this->client;
    }
    
    /**
     * Check if client is available
     */
    protected function hasClient(): bool
    {
        return $this->initializeClient();
    }
    
    /**
     * Handle StrixBudget exceptions with user-friendly messages
     */
    protected function handleStrixBudgetException(\Exception $e, string $defaultMessage = 'Възникна грешка при работа със StrixBudget API'): array
    {
        $message = $defaultMessage;
        
        // Check for specific error types
        if (strpos($e->getMessage(), 'Unauthenticated') !== false) {
            $message = 'Проблем с автентикацията. Моля, проверете настройките.';
        } elseif (strpos($e->getMessage(), 'Connection') !== false || strpos($e->getMessage(), 'cURL') !== false) {
            $message = 'Не може да се свърже със StrixBudget API. Моля, проверете мрежовата връзка и настройките.';
        } elseif (strpos($e->getMessage(), 'Invalid credentials') !== false) {
            $message = 'Невалидни потребителски данни. Моля, проверете email и парола в настройките.';
        }
        
        error_log("StrixBudget error: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
        
        return [
            'error' => $message,
            'technical_error' => $e->getMessage()
        ];
    }
    
    /**
     * Render view with common StrixBudget data
     */
    protected function renderStrixBudgetView(string $view, array $data = []): void
    {
        $data = array_merge([
            'showSidebar' => true,
            'hasStrixBudgetClient' => $this->hasClient()
        ], $data);
        
        $this->view($view, $data);
    }
}
