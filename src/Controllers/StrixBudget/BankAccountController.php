<?php

namespace Strix\ERP\Controllers\StrixBudget;

use Strix\ERP\Models\StrixBudget\BankAccount;

class BankAccountController extends BaseStrixBudgetController
{
    
    /**
     * List all bank accounts
     */
    public function index(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.view');
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $accounts = BankAccount::all();
            
            $this->view('admin/strixbudget/bank-accounts/index', [
                'title' => 'Банкови сметки',
                'showSidebar' => true,
                'accounts' => $accounts
            ]);
        } catch (\Exception $e) {
            $this->view('admin/strixbudget/bank-accounts/index', [
                'title' => 'Банкови сметки',
                'showSidebar' => true,
                'accounts' => [],
                'error' => 'Грешка при зареждане на банкови сметки: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Show create form
     */
    public function create(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        $this->view('admin/strixbudget/bank-accounts/create', [
            'title' => 'Създаване на банкова сметка',
            'showSidebar' => true,
            'currencies' => $this->getSupportedCurrencies(),
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }
    
    /**
     * Store new bank account
     */
    public function store(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/strixbudget/bank-accounts/create', 'error', 'Невалидна заявка');
        }
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        $data = $this->getAllInput();
        
        // Validate input
        $errors = $this->validate([
            'name' => 'required|max:255',
            'currency' => 'required|in:EUR,USD,BGN,GBP,CHF,JPY',
            'balance' => 'required|numeric'
        ]);
        
        if (!empty($errors)) {
            $this->view('admin/strixbudget/bank-accounts/create', [
                'title' => 'Създаване на банкова сметка',
                'showSidebar' => true,
                'currencies' => $this->getSupportedCurrencies(),
                'errors' => $errors,
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
            return;
        }
        
        try {
            $accountData = [
                'name' => $data['name'],
                'currency' => $data['currency'],
                'balance' => (float) $data['balance'],
                'is_active' => isset($data['is_active']) ? true : false,
                'is_default' => isset($data['is_default']) ? true : false
            ];
            
            $account = BankAccount::create($accountData);
            
            if ($account) {
                $this->redirectWithMessage('/admin/strixbudget/bank-accounts', 'success', 'Банковата сметка е създадена успешно');
            } else {
                throw new \Exception('Грешка при създаване на банковата сметка');
            }
        } catch (\Exception $e) {
            $this->view('admin/strixbudget/bank-accounts/create', [
                'title' => 'Създаване на банкова сметка',
                'showSidebar' => true,
                'currencies' => $this->getSupportedCurrencies(),
                'error' => 'Грешка при създаване: ' . $e->getMessage(),
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        }
    }
    
    /**
     * Show specific bank account
     */
    public function show(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.view');
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $account = BankAccount::find($id);
            
            if (!$account) {
                $this->redirectWithMessage('/admin/strixbudget/bank-accounts', 'error', 'Банковата сметка не е намерена');
            }
            
            $statistics = $account->getStatistics();
            
            $this->view('admin/strixbudget/bank-accounts/show', [
                'title' => 'Преглед на банкова сметка: ' . $account->getDisplayName(),
                'showSidebar' => true,
                'account' => $account,
                'statistics' => $statistics
            ]);
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/strixbudget/bank-accounts', 'error', 'Грешка при зареждане на банковата сметка: ' . $e->getMessage());
        }
    }
    
    /**
     * Show edit form
     */
    public function edit(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $account = BankAccount::find($id);
            
            if (!$account) {
                $this->redirectWithMessage('/admin/strixbudget/bank-accounts', 'error', 'Банковата сметка не е намерена');
            }
            
            $this->view('admin/strixbudget/bank-accounts/edit', [
                'title' => 'Редактиране на банкова сметка',
                'showSidebar' => true,
                'account' => $account,
                'currencies' => $this->getSupportedCurrencies(),
                'csrf_token' => $this->generateCsrfToken()
            ]);
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/strixbudget/bank-accounts', 'error', 'Грешка при зареждане на банковата сметка: ' . $e->getMessage());
        }
    }
    
    /**
     * Update bank account
     */
    public function update(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/strixbudget/bank-accounts', 'error', 'Невалидна заявка');
        }
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $account = BankAccount::find($id);
            
            if (!$account) {
                $this->redirectWithMessage('/admin/strixbudget/bank-accounts', 'error', 'Банковата сметка не е намерена');
            }
            
            $data = $this->getAllInput();
            
            // Validate input
            $errors = $this->validate([
                'name' => 'required|max:255',
                'currency' => 'required|in:EUR,USD,BGN,GBP,CHF,JPY',
                'balance' => 'required|numeric'
            ]);
            
            if (!empty($errors)) {
                $this->view('admin/strixbudget/bank-accounts/edit', [
                    'title' => 'Редактиране на банкова сметка',
                    'showSidebar' => true,
                    'account' => $account,
                    'currencies' => $this->getSupportedCurrencies(),
                    'errors' => $errors,
                    'old_input' => $data,
                    'csrf_token' => $this->generateCsrfToken()
                ]);
                return;
            }
            
            $account->name = $data['name'];
            $account->currency = $data['currency'];
            $account->balance = (float) $data['balance'];
            $account->is_active = isset($data['is_active']) ? true : false;
            $account->is_default = isset($data['is_default']) ? true : false;
            
            if ($account->save()) {
                $this->redirectWithMessage('/admin/strixbudget/bank-accounts/' . $id, 'success', 'Банковата сметка е обновена успешно');
            } else {
                throw new \Exception('Грешка при обновяване на банковата сметка');
            }
        } catch (\Exception $e) {
            $account = BankAccount::find($id);
            $data = $this->getAllInput();
            
            $this->view('admin/strixbudget/bank-accounts/edit', [
                'title' => 'Редактиране на банкова сметка',
                'showSidebar' => true,
                'account' => $account,
                'currencies' => $this->getSupportedCurrencies(),
                'error' => 'Грешка при обновяване: ' . $e->getMessage(),
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        }
    }
    
    /**
     * Delete bank account
     */
    public function delete(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->validateCsrfToken()) {
            $this->json(['error' => 'Невалидна заявка'], 400);
            return;
        }
        
        if (!$this->client) {
            $this->json(['error' => 'StrixBudget не е конфигуриран'], 400);
            return;
        }
        
        try {
            $account = BankAccount::find($id);
            
            if (!$account) {
                $this->json(['error' => 'Банковата сметка не е намерена'], 404);
                return;
            }
            
            if ($account->delete()) {
                $this->json(['success' => true, 'message' => 'Банковата сметка е изтрита успешно']);
            } else {
                $this->json(['error' => 'Грешка при изтриване на банковата сметка'], 500);
            }
        } catch (\Exception $e) {
            $this->json(['error' => 'Грешка: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Get supported currencies
     */
    private function getSupportedCurrencies(): array
    {
        return [
            'EUR' => 'Euro (EUR)',
            'USD' => 'US Dollar (USD)',
            'BGN' => 'Bulgarian Lev (BGN)',
            'GBP' => 'British Pound (GBP)',
            'CHF' => 'Swiss Franc (CHF)',
            'JPY' => 'Japanese Yen (JPY)'
        ];
    }
}
