<?php

namespace Strix\ERP\Controllers\StrixBudget;

use Strix\ERP\Models\StrixBudget\Transaction;
use Strix\ERP\Models\StrixBudget\BankAccount;
use Strix\ERP\Models\StrixBudget\Counterparty;
use Strix\ERP\Models\StrixBudget\TransactionType;

class TransactionController extends BaseStrixBudgetController
{
    /**
     * List transactions with filters
     */
    public function index(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.view');
        
        $this->requireStrixBudgetClient();
        
        // Get filter parameters
        $filters = [
            'bank_account_id' => $this->getInput('bank_account_id', ''),
            'type' => $this->getInput('type', ''),
            'counterparty_id' => $this->getInput('counterparty_id', ''),
            'transaction_type_id' => $this->getInput('transaction_type_id', ''),
            'from_date' => $this->getInput('from_date', ''),
            'to_date' => $this->getInput('to_date', ''),
            'per_page' => min(100, max(1, (int) $this->getInput('per_page', 20)))
        ];
        
        // Remove empty filters
        $filters = array_filter($filters, function($value) {
            return $value !== '' && $value !== null;
        });
        
        try {
            $transactions = Transaction::getFiltered($filters);
            $bankAccounts = BankAccount::all();
            $counterparties = Counterparty::all();
            $transactionTypes = TransactionType::all();
            
            $this->renderStrixBudgetView('admin/strixbudget/transactions/index', [
                'title' => 'Транзакции',
                'transactions' => $transactions,
                'bankAccounts' => $bankAccounts,
                'counterparties' => $counterparties,
                'transactionTypes' => $transactionTypes,
                'filters' => $filters
            ]);
        } catch (\Exception $e) {
            $errorInfo = $this->handleStrixBudgetException($e, 'Грешка при зареждане на транзакции');

            $this->renderStrixBudgetView('admin/strixbudget/transactions/index', [
                'title' => 'Транзакции',
                'transactions' => [],
                'bankAccounts' => [],
                'counterparties' => [],
                'transactionTypes' => [],
                'filters' => $filters,
                'error' => $errorInfo['error']
            ]);
        }
    }
    
    /**
     * Show create form
     */
    public function create(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $bankAccounts = BankAccount::getActive();
            $counterparties = Counterparty::all();
            $transactionTypes = TransactionType::all();
            
            $this->view('admin/strixbudget/transactions/create', [
                'title' => 'Създаване на транзакция',
                'showSidebar' => true,
                'bankAccounts' => $bankAccounts,
                'counterparties' => $counterparties,
                'transactionTypes' => $transactionTypes,
                'currencies' => $this->getSupportedCurrencies(),
                'errors' => [],
                'old_input' => [],
                'csrf_token' => $this->generateCsrfToken()
            ]);
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/strixbudget/transactions', 'error', 'Грешка при зареждане на формата: ' . $e->getMessage());
        }
    }
    
    /**
     * Store new transaction
     */
    public function store(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/strixbudget/transactions/create', 'error', 'Невалидна заявка');
        }
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        $data = $this->getAllInput();
        
        // Validate input
        $errors = $this->validate([
            'bank_account_id' => 'required|numeric',
            'type' => 'required|in:income,expense',
            'amount' => 'required|numeric|min:0.01',
            'currency' => 'required|in:EUR,USD,BGN,GBP,CHF,JPY',
            'executed_at' => 'required|date'
        ]);
        
        if (!empty($errors)) {
            $this->showCreateFormWithErrors($errors, $data);
            return;
        }
        
        try {
            $transactionData = [
                'bank_account_id' => (int) $data['bank_account_id'],
                'type' => $data['type'],
                'amount' => (float) $data['amount'],
                'currency' => $data['currency'],
                'executed_at' => $data['executed_at'],
                'description' => $data['description'] ?? ''
            ];
            
            // Add optional fields
            if (!empty($data['counterparty_id'])) {
                $transactionData['counterparty_id'] = (int) $data['counterparty_id'];
            }
            
            if (!empty($data['transaction_type_id'])) {
                $transactionData['transaction_type_id'] = (int) $data['transaction_type_id'];
            }
            
            $transaction = Transaction::create($transactionData);
            
            if ($transaction) {
                $this->redirectWithMessage('/admin/strixbudget/transactions', 'success', 'Транзакцията е създадена успешно');
            } else {
                throw new \Exception('Грешка при създаване на транзакцията');
            }
        } catch (\Exception $e) {
            $this->showCreateFormWithErrors(['general' => 'Грешка при създаване: ' . $e->getMessage()], $data);
        }
    }
    
    /**
     * Show specific transaction
     */
    public function show(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.view');
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $transaction = Transaction::find($id);
            
            if (!$transaction) {
                $this->redirectWithMessage('/admin/strixbudget/transactions', 'error', 'Транзакцията не е намерена');
            }
            
            $this->view('admin/strixbudget/transactions/show', [
                'title' => 'Преглед на транзакция',
                'showSidebar' => true,
                'transaction' => $transaction
            ]);
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/strixbudget/transactions', 'error', 'Грешка при зареждане на транзакцията: ' . $e->getMessage());
        }
    }
    
    /**
     * Show edit form
     */
    public function edit(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $transaction = Transaction::find($id);
            
            if (!$transaction) {
                $this->redirectWithMessage('/admin/strixbudget/transactions', 'error', 'Транзакцията не е намерена');
            }
            
            $bankAccounts = BankAccount::getActive();
            $counterparties = Counterparty::all();
            $transactionTypes = TransactionType::all();
            
            $this->view('admin/strixbudget/transactions/edit', [
                'title' => 'Редактиране на транзакция',
                'showSidebar' => true,
                'transaction' => $transaction,
                'bankAccounts' => $bankAccounts,
                'counterparties' => $counterparties,
                'transactionTypes' => $transactionTypes,
                'currencies' => $this->getSupportedCurrencies(),
                'csrf_token' => $this->generateCsrfToken()
            ]);
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/strixbudget/transactions', 'error', 'Грешка при зареждане на транзакцията: ' . $e->getMessage());
        }
    }
    
    /**
     * Update transaction
     */
    public function update(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/strixbudget/transactions', 'error', 'Невалидна заявка');
        }
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $transaction = Transaction::find($id);
            
            if (!$transaction) {
                $this->redirectWithMessage('/admin/strixbudget/transactions', 'error', 'Транзакцията не е намерена');
            }
            
            $data = $this->getAllInput();
            
            // Validate input
            $errors = $this->validate([
                'bank_account_id' => 'required|numeric',
                'type' => 'required|in:income,expense',
                'amount' => 'required|numeric|min:0.01',
                'currency' => 'required|in:EUR,USD,BGN,GBP,CHF,JPY',
                'executed_at' => 'required|date'
            ]);
            
            if (!empty($errors)) {
                $this->showEditFormWithErrors($transaction, $errors, $data);
                return;
            }
            
            $transaction->bank_account_id = (int) $data['bank_account_id'];
            $transaction->type = $data['type'];
            $transaction->amount = (float) $data['amount'];
            $transaction->currency = $data['currency'];
            $transaction->executed_at = $data['executed_at'];
            $transaction->description = $data['description'] ?? '';
            
            // Update optional fields
            $transaction->counterparty_id = !empty($data['counterparty_id']) ? (int) $data['counterparty_id'] : null;
            $transaction->transaction_type_id = !empty($data['transaction_type_id']) ? (int) $data['transaction_type_id'] : null;
            
            if ($transaction->save()) {
                $this->redirectWithMessage('/admin/strixbudget/transactions/' . $id, 'success', 'Транзакцията е обновена успешно');
            } else {
                throw new \Exception('Грешка при обновяване на транзакцията');
            }
        } catch (\Exception $e) {
            $transaction = Transaction::find($id);
            $data = $this->getAllInput();
            $this->showEditFormWithErrors($transaction, ['general' => 'Грешка при обновяване: ' . $e->getMessage()], $data);
        }
    }
    
    /**
     * Delete transaction
     */
    public function delete(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->validateCsrfToken()) {
            $this->json(['error' => 'Невалидна заявка'], 400);
            return;
        }
        
        if (!$this->client) {
            $this->json(['error' => 'StrixBudget не е конфигуриран'], 400);
            return;
        }
        
        try {
            $transaction = Transaction::find($id);
            
            if (!$transaction) {
                $this->json(['error' => 'Транзакцията не е намерена'], 404);
                return;
            }
            
            if ($transaction->delete()) {
                $this->json(['success' => true, 'message' => 'Транзакцията е изтрита успешно']);
            } else {
                $this->json(['error' => 'Грешка при изтриване на транзакцията'], 500);
            }
        } catch (\Exception $e) {
            $this->json(['error' => 'Грешка: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Show create form with errors
     */
    private function showCreateFormWithErrors(array $errors, array $oldInput): void
    {
        try {
            $bankAccounts = BankAccount::getActive();
            $counterparties = Counterparty::all();
            $transactionTypes = TransactionType::all();

            $this->view('admin/strixbudget/transactions/create', [
                'title' => 'Създаване на транзакция',
                'showSidebar' => true,
                'bankAccounts' => $bankAccounts,
                'counterparties' => $counterparties,
                'transactionTypes' => $transactionTypes,
                'currencies' => $this->getSupportedCurrencies(),
                'errors' => $errors,
                'old_input' => $oldInput,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/strixbudget/transactions', 'error', 'Грешка при зареждане на формата: ' . $e->getMessage());
        }
    }

    /**
     * Show edit form with errors
     */
    private function showEditFormWithErrors($transaction, array $errors, array $oldInput): void
    {
        try {
            $bankAccounts = BankAccount::getActive();
            $counterparties = Counterparty::all();
            $transactionTypes = TransactionType::all();

            $this->view('admin/strixbudget/transactions/edit', [
                'title' => 'Редактиране на транзакция',
                'showSidebar' => true,
                'transaction' => $transaction,
                'bankAccounts' => $bankAccounts,
                'counterparties' => $counterparties,
                'transactionTypes' => $transactionTypes,
                'currencies' => $this->getSupportedCurrencies(),
                'errors' => $errors,
                'old_input' => $oldInput,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/strixbudget/transactions', 'error', 'Грешка при зареждане на формата: ' . $e->getMessage());
        }
    }

    /**
     * Get supported currencies
     */
    private function getSupportedCurrencies(): array
    {
        return [
            'EUR' => 'Euro (EUR)',
            'USD' => 'US Dollar (USD)',
            'BGN' => 'Bulgarian Lev (BGN)',
            'GBP' => 'British Pound (GBP)',
            'CHF' => 'Swiss Franc (CHF)',
            'JPY' => 'Japanese Yen (JPY)'
        ];
    }
}
