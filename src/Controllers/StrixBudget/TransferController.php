<?php

namespace Strix\ERP\Controllers\StrixBudget;

use Strix\ERP\Models\StrixBudget\Transfer;
use Strix\ERP\Models\StrixBudget\BankAccount;

class TransferController extends BaseStrixBudgetController
{
    /**
     * List all transfers
     */
    public function index(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.view');
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $transfers = Transfer::all();
            
            $this->view('admin/strixbudget/transfers/index', [
                'title' => 'Трансфери',
                'showSidebar' => true,
                'transfers' => $transfers
            ]);
        } catch (\Exception $e) {
            $this->view('admin/strixbudget/transfers/index', [
                'title' => 'Трансфери',
                'showSidebar' => true,
                'transfers' => [],
                'error' => 'Грешка при зареждане на трансфери: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Show create form
     */
    public function create(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $bankAccounts = BankAccount::getActive();
            
            $this->view('admin/strixbudget/transfers/create', [
                'title' => 'Създаване на трансфер',
                'showSidebar' => true,
                'bankAccounts' => $bankAccounts,
                'currencies' => $this->getSupportedCurrencies(),
                'errors' => [],
                'old_input' => [],
                'csrf_token' => $this->generateCsrfToken()
            ]);
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/strixbudget/transfers', 'error', 'Грешка при зареждане на формата: ' . $e->getMessage());
        }
    }
    
    /**
     * Store new transfer
     */
    public function store(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/strixbudget/transfers/create', 'error', 'Невалидна заявка');
        }
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        $data = $this->getAllInput();
        
        // Validate input
        $errors = $this->validate([
            'from_account_id' => 'required|numeric',
            'to_account_id' => 'required|numeric',
            'amount_from' => 'required|numeric|min:0.01',
            'currency_from' => 'required|in:EUR,USD,BGN,GBP,CHF,JPY',
            'amount_to' => 'required|numeric|min:0.01',
            'currency_to' => 'required|in:EUR,USD,BGN,GBP,CHF,JPY',
            'executed_at' => 'required|date'
        ]);
        
        // Additional validation
        if ($data['from_account_id'] === $data['to_account_id']) {
            $errors['to_account_id'] = 'Сметката за получаване трябва да е различна от сметката за изпращане';
        }
        
        if (!empty($errors)) {
            $this->showCreateFormWithErrors($errors, $data);
            return;
        }
        
        try {
            $transferData = [
                'from_account_id' => (int) $data['from_account_id'],
                'to_account_id' => (int) $data['to_account_id'],
                'amount_from' => (float) $data['amount_from'],
                'currency_from' => $data['currency_from'],
                'amount_to' => (float) $data['amount_to'],
                'currency_to' => $data['currency_to'],
                'executed_at' => $data['executed_at'],
                'description' => $data['description'] ?? ''
            ];
            
            // Calculate exchange rate if currencies are different
            if ($data['currency_from'] !== $data['currency_to']) {
                $transferData['exchange_rate'] = (float) $data['amount_to'] / (float) $data['amount_from'];
            } else {
                $transferData['exchange_rate'] = 1.0;
            }
            
            $transfer = Transfer::create($transferData);
            
            if ($transfer) {
                $this->redirectWithMessage('/admin/strixbudget/transfers', 'success', 'Трансферът е създаден успешно');
            } else {
                throw new \Exception('Грешка при създаване на трансфера');
            }
        } catch (\Exception $e) {
            $this->showCreateFormWithErrors(['general' => 'Грешка при създаване: ' . $e->getMessage()], $data);
        }
    }
    
    /**
     * Show specific transfer
     */
    public function show(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.view');
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $transfer = Transfer::find($id);
            
            if (!$transfer) {
                $this->redirectWithMessage('/admin/strixbudget/transfers', 'error', 'Трансферът не е намерен');
            }
            
            $this->view('admin/strixbudget/transfers/show', [
                'title' => 'Преглед на трансфер',
                'showSidebar' => true,
                'transfer' => $transfer
            ]);
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/strixbudget/transfers', 'error', 'Грешка при зареждане на трансфера: ' . $e->getMessage());
        }
    }
    
    /**
     * Show edit form
     */
    public function edit(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $transfer = Transfer::find($id);
            
            if (!$transfer) {
                $this->redirectWithMessage('/admin/strixbudget/transfers', 'error', 'Трансферът не е намерен');
            }
            
            $bankAccounts = BankAccount::getActive();
            
            $this->view('admin/strixbudget/transfers/edit', [
                'title' => 'Редактиране на трансфер',
                'showSidebar' => true,
                'transfer' => $transfer,
                'bankAccounts' => $bankAccounts,
                'currencies' => $this->getSupportedCurrencies(),
                'csrf_token' => $this->generateCsrfToken()
            ]);
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/strixbudget/transfers', 'error', 'Грешка при зареждане на трансфера: ' . $e->getMessage());
        }
    }
    
    /**
     * Update transfer
     */
    public function update(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/strixbudget/transfers', 'error', 'Невалидна заявка');
        }
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $transfer = Transfer::find($id);
            
            if (!$transfer) {
                $this->redirectWithMessage('/admin/strixbudget/transfers', 'error', 'Трансферът не е намерен');
            }
            
            $data = $this->getAllInput();
            
            // Validate input
            $errors = $this->validate([
                'from_account_id' => 'required|numeric',
                'to_account_id' => 'required|numeric',
                'amount_from' => 'required|numeric|min:0.01',
                'currency_from' => 'required|in:EUR,USD,BGN,GBP,CHF,JPY',
                'amount_to' => 'required|numeric|min:0.01',
                'currency_to' => 'required|in:EUR,USD,BGN,GBP,CHF,JPY',
                'executed_at' => 'required|date'
            ]);
            
            // Additional validation
            if ($data['from_account_id'] === $data['to_account_id']) {
                $errors['to_account_id'] = 'Сметката за получаване трябва да е различна от сметката за изпращане';
            }
            
            if (!empty($errors)) {
                $this->showEditFormWithErrors($transfer, $errors, $data);
                return;
            }
            
            $transfer->from_account_id = (int) $data['from_account_id'];
            $transfer->to_account_id = (int) $data['to_account_id'];
            $transfer->amount_from = (float) $data['amount_from'];
            $transfer->currency_from = $data['currency_from'];
            $transfer->amount_to = (float) $data['amount_to'];
            $transfer->currency_to = $data['currency_to'];
            $transfer->executed_at = $data['executed_at'];
            $transfer->description = $data['description'] ?? '';
            
            // Calculate exchange rate if currencies are different
            if ($data['currency_from'] !== $data['currency_to']) {
                $transfer->exchange_rate = (float) $data['amount_to'] / (float) $data['amount_from'];
            } else {
                $transfer->exchange_rate = 1.0;
            }
            
            if ($transfer->save()) {
                $this->redirectWithMessage('/admin/strixbudget/transfers/' . $id, 'success', 'Трансферът е обновен успешно');
            } else {
                throw new \Exception('Грешка при обновяване на трансфера');
            }
        } catch (\Exception $e) {
            $transfer = Transfer::find($id);
            $data = $this->getAllInput();
            $this->showEditFormWithErrors($transfer, ['general' => 'Грешка при обновяване: ' . $e->getMessage()], $data);
        }
    }
    
    /**
     * Delete transfer
     */
    public function delete(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->validateCsrfToken()) {
            $this->json(['error' => 'Невалидна заявка'], 400);
            return;
        }
        
        if (!$this->client) {
            $this->json(['error' => 'StrixBudget не е конфигуриран'], 400);
            return;
        }
        
        try {
            $transfer = Transfer::find($id);
            
            if (!$transfer) {
                $this->json(['error' => 'Трансферът не е намерен'], 404);
                return;
            }
            
            if ($transfer->delete()) {
                $this->json(['success' => true, 'message' => 'Трансферът е изтрит успешно']);
            } else {
                $this->json(['error' => 'Грешка при изтриване на трансфера'], 500);
            }
        } catch (\Exception $e) {
            $this->json(['error' => 'Грешка: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Show create form with errors
     */
    private function showCreateFormWithErrors(array $errors, array $oldInput): void
    {
        try {
            $bankAccounts = BankAccount::getActive();

            $this->view('admin/strixbudget/transfers/create', [
                'title' => 'Създаване на трансфер',
                'showSidebar' => true,
                'bankAccounts' => $bankAccounts,
                'currencies' => $this->getSupportedCurrencies(),
                'errors' => $errors,
                'old_input' => $oldInput,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/strixbudget/transfers', 'error', 'Грешка при зареждане на формата: ' . $e->getMessage());
        }
    }

    /**
     * Show edit form with errors
     */
    private function showEditFormWithErrors($transfer, array $errors, array $oldInput): void
    {
        try {
            $bankAccounts = BankAccount::getActive();

            $this->view('admin/strixbudget/transfers/edit', [
                'title' => 'Редактиране на трансфер',
                'showSidebar' => true,
                'transfer' => $transfer,
                'bankAccounts' => $bankAccounts,
                'currencies' => $this->getSupportedCurrencies(),
                'errors' => $errors,
                'old_input' => $oldInput,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/strixbudget/transfers', 'error', 'Грешка при зареждане на формата: ' . $e->getMessage());
        }
    }

    /**
     * Get supported currencies
     */
    private function getSupportedCurrencies(): array
    {
        return [
            'EUR' => 'Euro (EUR)',
            'USD' => 'US Dollar (USD)',
            'BGN' => 'Bulgarian Lev (BGN)',
            'GBP' => 'British Pound (GBP)',
            'CHF' => 'Swiss Franc (CHF)',
            'JPY' => 'Japanese Yen (JPY)'
        ];
    }
}
