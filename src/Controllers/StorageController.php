<?php

namespace Strix\ERP\Controllers;

use Strix\ERP\Core\Controller;
use Strix\ERP\Services\NextcloudClient;
use Strix\ERP\Services\MockNextcloudClient;
use Strix\ERP\Models\UserNextcloudSettings;
use Exception;

class StorageController extends Controller
{
    private NextcloudClient|MockNextcloudClient|null $nextcloud = null;
    
    public function __construct()
    {
        parent::__construct();
        // Nextcloud client will be initialized per request based on user settings
    }

    /**
     * Get Nextcloud client for current user
     */
    private function getNextcloudClient(): NextcloudClient|MockNextcloudClient
    {
        if ($this->nextcloud) {
            return $this->nextcloud;
        }

        $currentUser = $this->app->getCurrentUser();

        // Check if we should force real client for debugging
        $forceReal = isset($_GET['force_real']) || isset($_POST['force_real']);

        try {
            // Try to get user-specific settings
            $userSettings = null;
            if ($currentUser && isset($currentUser['id'])) {
                $userSettings = UserNextcloudSettings::getActiveForUser($currentUser['id']);
            }

            if ($userSettings && $userSettings->server_url && $userSettings->username) {
                // Always try user settings first if they exist
                $decryptedPassword = $userSettings->getDecryptedPassword();
                if ($decryptedPassword && ($forceReal || $this->isValidNextcloudConfig($userSettings->server_url, $userSettings->username, $decryptedPassword))) {
                    // Use user-specific Nextcloud client
                    $this->nextcloud = new NextcloudClient($userSettings);
                    if (!isset($_SESSION['real_client_notified'])) {
                        $this->app->setFlashMessage('success', 'Осъществена е връзка с  Nextcloud сървър.');
                        $_SESSION['real_client_notified'] = true;
                    }
                    return $this->nextcloud;
                } else {
                    // User settings exist but are invalid
                    if (!isset($_SESSION['invalid_user_settings_notified'])) {
                        $this->app->setFlashMessage('warning', 'Вашите Nextcloud настройки са невалидни. Моля, обновете ги в профила си.');
                        $_SESSION['invalid_user_settings_notified'] = true;
                    }
                }
            }

            // Check global configuration
            $config = require __DIR__ . '/../../config/nextcloud.php';
            $globalUrl = $config['server']['url'];
            $globalUsername = $config['auth']['username'];
            $globalPassword = $config['auth']['password'];

            if ($forceReal || $this->isValidNextcloudConfig($globalUrl, $globalUsername, $globalPassword)) {
                // Fallback to global configuration
                $this->nextcloud = new NextcloudClient();
                if (!isset($_SESSION['real_client_notified'])) {
                    $this->app->setFlashMessage('warning', 'Използва се истински Nextcloud клиент с глобални настройки. Препоръчваме да конфигурирате потребителски настройки.');
                    $_SESSION['real_client_notified'] = true;
                }
                return $this->nextcloud;
            }

            // If no valid configuration, use mock client
            throw new Exception('Няма валидна Nextcloud конфигурация');

        } catch (Exception $e) {
            // If real client fails, use mock client for demonstration
            try {
                $this->nextcloud = new MockNextcloudClient();
                if (!isset($_SESSION['mock_mode_notified'])) {
                    $this->app->setFlashMessage('info', 'Използва се демо режим с примерни файлове. За работа с истински Nextcloud сървър, конфигурирайте настройките в профила си.');
                    $_SESSION['mock_mode_notified'] = true;
                }
                return $this->nextcloud;
            } catch (Exception $mockError) {
                throw new Exception('Грешка при инициализиране на файловия мениджър: ' . $mockError->getMessage());
            }
        }
    }

    /**
     * Check if Nextcloud configuration is valid
     */
    private function isValidNextcloudConfig(string $url, string $username, string $password): bool
    {
        // Check for placeholder values
        $placeholders = [
            'https://your-nextcloud-server.com',
            'your-nextcloud-server.com',
            'your-username',
            'your-app-password',
            'your-password',
            ''
        ];

        if (in_array($url, $placeholders) ||
            in_array($username, $placeholders) ||
            in_array($password, $placeholders)) {
            return false;
        }

        // Basic URL validation
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }

        return true;
    }
    
    /**
     * Display file browser
     */
    public function index(): void
    {
        $this->requireAuth();
        $this->requirePermission('storage.view');
        
        $path = $this->getInput('path', '/');
        $view = $this->getInput('view', 'list'); // list or grid
        
        try {
            $nextcloud = $this->getNextcloudClient();
            $items = $nextcloud->listDirectory($path);

            // Separate folders and files
            $folders = array_filter($items, fn($item) => $item['is_directory']);
            $files = array_filter($items, fn($item) => !$item['is_directory']);
            
            // Sort items
            usort($folders, fn($a, $b) => strcasecmp($a['name'], $b['name']));
            usort($files, fn($a, $b) => strcasecmp($a['name'], $b['name']));
            
            // Combine folders first, then files
            $sortedItems = array_merge($folders, $files);
            
            // Add breadcrumb navigation
            $breadcrumbs = $this->generateBreadcrumbs($path);
            
            $this->view('admin/storage/index', [
                'title' => 'Файлов мениджър',
                'showSidebar' => true,
                'items' => $sortedItems,
                'currentPath' => $path,
                'breadcrumbs' => $breadcrumbs,
                'view' => $view,
                'csrf_token' => $this->generateCsrfToken()
            ]);
            
        } catch (Exception $e) {
            $this->view('admin/storage/error', [
                'title' => 'Грешка в файловия мениджър',
                'showSidebar' => true,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Upload files
     */
    public function upload(): void
    {
        $this->requireAuth();
        $this->requirePermission('storage.upload');
        
        if (!$this->validateCsrfToken()) {
            $this->json(['error' => 'Невалидна заявка'], 400);
            return;
        }
        
        $path = $this->getInput('path', '/');
        
        if (!isset($_FILES['files'])) {
            $this->json(['error' => 'Няма избрани файлове'], 400);
            return;
        }
        
        $files = $_FILES['files'];
        $uploadedFiles = [];
        $errors = [];
        
        // Handle multiple files
        if (is_array($files['name'])) {
            for ($i = 0; $i < count($files['name']); $i++) {
                $file = [
                    'name' => $files['name'][$i],
                    'tmp_name' => $files['tmp_name'][$i],
                    'size' => $files['size'][$i],
                    'error' => $files['error'][$i]
                ];
                
                $result = $this->uploadSingleFile($file, $path);
                if ($result['success']) {
                    $uploadedFiles[] = $result['filename'];
                } else {
                    $errors[] = $result['error'];
                }
            }
        } else {
            $result = $this->uploadSingleFile($files, $path);
            if ($result['success']) {
                $uploadedFiles[] = $result['filename'];
            } else {
                $errors[] = $result['error'];
            }
        }
        
        if (!empty($uploadedFiles)) {
            $message = 'Успешно качени файлове: ' . implode(', ', $uploadedFiles);
            if (!empty($errors)) {
                $message .= '. Грешки: ' . implode(', ', $errors);
            }
            $this->json(['success' => true, 'message' => $message]);
        } else {
            $this->json(['error' => 'Неуспешно качване: ' . implode(', ', $errors)], 400);
        }
    }
    
    /**
     * Download file
     */
    public function download(): void
    {
        $this->requireAuth();
        $this->requirePermission('storage.download');
        
        $path = $this->getInput('path');
        
        if (!$path) {
            $this->redirectWithMessage('/admin/storage', 'error', 'Невалиден път до файл');
            return;
        }
        
        try {
            $nextcloud = $this->getNextcloudClient();

            // Check if file exists first
            if (!$nextcloud->exists($path)) {
                $this->redirectWithMessage('/admin/storage', 'error', 'Файлът не съществува: ' . $path);
                return;
            }

            $content = $nextcloud->downloadFile($path);

            if ($content === false || empty($content)) {
                $this->redirectWithMessage('/admin/storage', 'error', 'Файлът не може да бъде изтеглен или е празен');
                return;
            }
            
            $filename = basename($path);
            $mimeType = $this->getMimeType($filename);
            
            header('Content-Type: ' . $mimeType);
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Length: ' . strlen($content));
            
            echo $content;
            exit;
            
        } catch (Exception $e) {
            $this->redirectWithMessage('/admin/storage', 'error', 'Грешка при изтегляне: ' . $e->getMessage());
        }
    }
    
    /**
     * Create new folder
     */
    public function createFolder(): void
    {
        $this->requireAuth();
        $this->requirePermission('storage.create');
        
        if (!$this->validateCsrfToken()) {
            $this->json(['error' => 'Невалидна заявка'], 400);
            return;
        }
        
        $path = $this->getInput('path', '/');
        $folderName = trim($this->getInput('folder_name'));
        
        if (empty($folderName)) {
            $this->json(['error' => 'Името на папката не може да бъде празно'], 400);
            return;
        }
        
        // Validate folder name
        if (!$this->isValidFilename($folderName)) {
            $this->json(['error' => 'Невалидно име на папка'], 400);
            return;
        }
        
        $fullPath = rtrim($path, '/') . '/' . $folderName;
        
        try {
            $nextcloud = $this->getNextcloudClient();

            if ($nextcloud->exists($fullPath)) {
                $this->json(['error' => 'Папка с това име вече съществува'], 400);
                return;
            }

            if ($nextcloud->createDirectory($fullPath)) {
                $this->json(['success' => true, 'message' => 'Папката е създадена успешно']);
            } else {
                $this->json(['error' => 'Грешка при създаване на папката'], 500);
            }

        } catch (Exception $e) {
            $this->json(['error' => 'Грешка при създаване на папката: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Delete file or folder
     */
    public function delete(): void
    {
        $this->requireAuth();
        $this->requirePermission('storage.delete');
        
        if (!$this->validateCsrfToken()) {
            $this->json(['error' => 'Невалидна заявка'], 400);
            return;
        }
        
        $path = $this->getInput('path');
        
        if (!$path || $path === '/') {
            $this->json(['error' => 'Невалиден път'], 400);
            return;
        }
        
        try {
            $nextcloud = $this->getNextcloudClient();

            if ($nextcloud->delete($path)) {
                $this->json(['success' => true, 'message' => 'Файлът/папката е изтрит успешно']);
            } else {
                $this->json(['error' => 'Грешка при изтриване'], 500);
            }

        } catch (Exception $e) {
            $this->json(['error' => 'Грешка при изтриване: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Rename file or folder
     */
    public function rename(): void
    {
        $this->requireAuth();
        $this->requirePermission('storage.edit');
        
        if (!$this->validateCsrfToken()) {
            $this->json(['error' => 'Невалидна заявка'], 400);
            return;
        }
        
        $oldPath = $this->getInput('old_path');
        $newName = trim($this->getInput('new_name'));
        
        if (!$oldPath || empty($newName)) {
            $this->json(['error' => 'Невалидни параметри'], 400);
            return;
        }
        
        if (!$this->isValidFilename($newName)) {
            $this->json(['error' => 'Невалидно име'], 400);
            return;
        }
        
        $newPath = dirname($oldPath) . '/' . $newName;
        
        try {
            $nextcloud = $this->getNextcloudClient();

            if ($nextcloud->exists($newPath)) {
                $this->json(['error' => 'Файл/папка с това име вече съществува'], 400);
                return;
            }

            if ($nextcloud->move($oldPath, $newPath)) {
                $this->json(['success' => true, 'message' => 'Преименуването е успешно']);
            } else {
                $this->json(['error' => 'Грешка при преименуване'], 500);
            }

        } catch (Exception $e) {
            $this->json(['error' => 'Грешка при преименуване: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Upload single file
     */
    private function uploadSingleFile(array $file, string $path): array
    {
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'error' => 'Грешка при качване на файла'];
        }
        
        $filename = $file['name'];
        $tmpPath = $file['tmp_name'];
        
        // Validate file
        if (!$this->isValidFile($filename, $file['size'])) {
            return ['success' => false, 'error' => "Невалиден файл: $filename"];
        }
        
        $remotePath = rtrim($path, '/') . '/' . $filename;
        
        try {
            $nextcloud = $this->getNextcloudClient();
            if ($nextcloud->uploadFile($tmpPath, $remotePath)) {
                return ['success' => true, 'filename' => $filename];
            } else {
                return ['success' => false, 'error' => "Грешка при качване на $filename"];
            }
        } catch (Exception $e) {
            return ['success' => false, 'error' => "Грешка при качване на $filename: " . $e->getMessage()];
        }
    }
    
    /**
     * Validate file
     */
    private function isValidFile(string $filename, int $size): bool
    {
        $config = require __DIR__ . '/../../config/nextcloud.php';
        
        // Check file size
        if ($size > $config['files']['max_upload_size']) {
            return false;
        }
        
        // Check file extension
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        if (in_array($extension, $config['files']['blocked_extensions'])) {
            return false;
        }
        
        if (!empty($config['files']['allowed_extensions']) && 
            !in_array($extension, $config['files']['allowed_extensions'])) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate filename
     */
    private function isValidFilename(string $filename): bool
    {
        // Check for invalid characters
        $invalidChars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|'];
        foreach ($invalidChars as $char) {
            if (strpos($filename, $char) !== false) {
                return false;
            }
        }
        
        // Check for reserved names
        $reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
        if (in_array(strtoupper($filename), $reservedNames)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Generate breadcrumb navigation
     */
    private function generateBreadcrumbs(string $path): array
    {
        $breadcrumbs = [['name' => 'Начало', 'path' => '/']];
        
        if ($path !== '/') {
            $parts = explode('/', trim($path, '/'));
            $currentPath = '';
            
            foreach ($parts as $part) {
                if (!empty($part)) {
                    $currentPath .= '/' . $part;
                    $breadcrumbs[] = ['name' => $part, 'path' => $currentPath];
                }
            }
        }
        
        return $breadcrumbs;
    }
    
    /**
     * Get MIME type for file
     */
    private function getMimeType(string $filename): string
    {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        $mimeTypes = [
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt' => 'application/vnd.ms-powerpoint',
            'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'txt' => 'text/plain',
            'html' => 'text/html',
            'css' => 'text/css',
            'js' => 'application/javascript',
            'json' => 'application/json',
            'xml' => 'application/xml',
            'zip' => 'application/zip',
            'mp4' => 'video/mp4',
            'mp3' => 'audio/mpeg'
        ];
        
        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }
}
