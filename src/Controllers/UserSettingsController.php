<?php

namespace Strix\ERP\Controllers;

use Strix\ERP\Core\Controller;
use Strix\ERP\Models\User;
use Strix\ERP\Models\UserNextcloudSettings;
use Exception;

class UserSettingsController extends Controller
{
    /**
     * Show user profile/settings page
     */
    public function profile(): void
    {
        $this->requireAuth();
        
        $currentUser = $this->app->getCurrentUser();
        $user = User::find($currentUser['id']);
        
        if (!$user) {
            $this->redirectWithMessage('/admin', 'error', 'Потребителят не е намерен');
            return;
        }
        
        // Get Nextcloud settings
        $nextcloudSettings = UserNextcloudSettings::getActiveForUser($user->id);
        
        $this->view('admin/users/profile', [
            'title' => 'Моят профил',
            'showSidebar' => true,
            'user' => $user,
            'nextcloudSettings' => $nextcloudSettings,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }
    
    /**
     * Update user profile
     */
    public function updateProfile(): void
    {
        $this->requireAuth();
        
        if (!$this->validateCsrfToken()) {
            $this->json(['error' => 'Невалидна заявка'], 400);
            return;
        }
        
        $currentUser = $this->app->getCurrentUser();
        $user = User::find($currentUser['id']);
        
        if (!$user) {
            $this->json(['error' => 'Потребителят не е намерен'], 404);
            return;
        }
        
        try {
            $data = $this->getAllInput();
            
            // Update basic profile info
            if (isset($data['first_name'])) {
                $user->first_name = trim($data['first_name']);
            }
            
            if (isset($data['last_name'])) {
                $user->last_name = trim($data['last_name']);
            }
            
            if (isset($data['email'])) {
                $email = trim($data['email']);
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $this->json(['error' => 'Невалиден email адрес'], 400);
                    return;
                }
                $user->email = $email;
            }
            
            // Update password if provided
            if (!empty($data['new_password'])) {
                if (empty($data['current_password'])) {
                    $this->json(['error' => 'Моля въведете текущата парола'], 400);
                    return;
                }
                
                if (!password_verify($data['current_password'], $user->password)) {
                    $this->json(['error' => 'Неправилна текуща парола'], 400);
                    return;
                }
                
                if (strlen($data['new_password']) < 6) {
                    $this->json(['error' => 'Новата парола трябва да е поне 6 символа'], 400);
                    return;
                }
                
                if ($data['new_password'] !== $data['confirm_password']) {
                    $this->json(['error' => 'Паролите не съвпадат'], 400);
                    return;
                }
                
                $user->password = password_hash($data['new_password'], PASSWORD_DEFAULT);
            }
            
            $user->save();
            
            // Update session data
            $sessionData = $this->app->getCurrentUser();
            $sessionData['first_name'] = $user->first_name;
            $sessionData['last_name'] = $user->last_name;
            $sessionData['email'] = $user->email;
            $sessionData['full_name'] = $user->getFullName();
            $this->app->setCurrentUser($sessionData);
            
            $this->json(['success' => true, 'message' => 'Профилът е обновен успешно']);
            
        } catch (Exception $e) {
            $this->json(['error' => 'Грешка при обновяване: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Show Nextcloud settings
     */
    public function nextcloudSettings(): void
    {
        $this->requireAuth();
        $this->requirePermission('nextcloud.view_personal');
        
        $currentUser = $this->app->getCurrentUser();
        $settings = UserNextcloudSettings::getActiveForUser($currentUser['id']);
        
        $this->view('admin/users/nextcloud_settings', [
            'title' => 'Nextcloud настройки',
            'showSidebar' => true,
            'settings' => $settings,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }
    
    /**
     * Update Nextcloud settings
     */
    public function updateNextcloudSettings(): void
    {
        $this->requireAuth();
        $this->requirePermission('nextcloud.manage_personal');
        
        if (!$this->validateCsrfToken()) {
            $this->json(['error' => 'Невалидна заявка'], 400);
            return;
        }
        
        try {
            $currentUser = $this->app->getCurrentUser();
            $data = $this->getAllInput();
            
            // Validate required fields
            if (empty($data['server_url'])) {
                $this->json(['error' => 'Server URL е задължителен'], 400);
                return;
            }
            
            if (empty($data['username'])) {
                $this->json(['error' => 'Потребителското име е задължително'], 400);
                return;
            }
            
            // Validate URL format
            if (!filter_var($data['server_url'], FILTER_VALIDATE_URL)) {
                $this->json(['error' => 'Невалиден Server URL'], 400);
                return;
            }
            
            // Prepare settings data
            $settingsData = [
                'server_url' => rtrim($data['server_url'], '/'),
                'username' => trim($data['username']),
                'verify_ssl' => !empty($data['verify_ssl']),
                'timeout' => (int) ($data['timeout'] ?: 30),
                'default_folder' => trim($data['default_folder'] ?: '/ERP_Files'),
                'auto_create_folders' => !empty($data['auto_create_folders'])
            ];
            
            // Add password if provided
            if (!empty($data['password'])) {
                $settingsData['password'] = $data['password'];
            }
            
            // Create or update settings
            $settings = UserNextcloudSettings::createOrUpdate($currentUser['id'], $settingsData);
            
            $this->json([
                'success' => true, 
                'message' => 'Nextcloud настройките са запазени успешно',
                'settings_id' => $settings->id
            ]);
            
        } catch (Exception $e) {
            $this->json(['error' => 'Грешка при запазване: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Test Nextcloud connection
     */
    public function testNextcloudConnection(): void
    {
        $this->requireAuth();
        $this->requirePermission('nextcloud.test_connection');

        if (!$this->validateCsrfToken()) {
            $this->json(['error' => 'Невалидна заявка'], 400);
            return;
        }

        try {
            $currentUser = $this->app->getCurrentUser();
            $data = $this->getAllInput();

            // Check if we have test data in the request (for testing before saving)
            if (!empty($data['server_url']) && !empty($data['username']) && !empty($data['password'])) {
                // Test with provided data (temporary test)
                $result = $this->testConnectionWithData($data);
            } else {
                // Test with saved settings
                $settings = UserNextcloudSettings::getActiveForUser($currentUser['id']);

                if (!$settings) {
                    $this->json(['error' => 'Няма конфигурирани Nextcloud настройки'], 400);
                    return;
                }

                $result = $settings->testConnection();
            }

            if ($result['success']) {
                $this->json([
                    'success' => true,
                    'message' => 'Връзката с Nextcloud е успешна!',
                    'details' => $result
                ]);
            } else {
                $this->json([
                    'success' => false,
                    'error' => $result['error'],
                    'details' => $result
                ], 400);
            }

        } catch (Exception $e) {
            $this->json(['error' => 'Грешка при тестване: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Test connection with provided data (without saving)
     */
    private function testConnectionWithData(array $data): array
    {
        try {
            $serverUrl = rtrim($data['server_url'], '/');
            $username = $data['username'];
            $password = $data['password'];
            $verifySsl = !empty($data['verify_ssl']);
            $timeout = (int) ($data['timeout'] ?: 30);

            if (empty($serverUrl) || empty($username) || empty($password)) {
                throw new Exception('Server URL, username and password are required');
            }

            // Test basic connection by listing root directory
            $ch = curl_init();
            $url = $serverUrl . '/remote.php/dav/files/' . $username . '/';

            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_CUSTOMREQUEST => 'PROPFIND',
                CURLOPT_HTTPHEADER => [
                    'Authorization: Basic ' . base64_encode($username . ':' . $password),
                    'Content-Type: application/xml',
                    'Depth: 1'
                ],
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => $timeout,
                CURLOPT_SSL_VERIFYPEER => $verifySsl,
                CURLOPT_POSTFIELDS => '<?xml version="1.0"?>
                    <d:propfind xmlns:d="DAV:">
                        <d:prop>
                            <d:displayname />
                        </d:prop>
                    </d:propfind>'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                throw new Exception("Connection error: $error");
            }

            if ($httpCode >= 400) {
                throw new Exception("HTTP error: $httpCode");
            }

            return [
                'success' => true,
                'message' => 'Connection successful',
                'http_code' => $httpCode
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Delete Nextcloud settings
     */
    public function deleteNextcloudSettings(): void
    {
        $this->requireAuth();
        $this->requirePermission('nextcloud.manage_personal');
        
        if (!$this->validateCsrfToken()) {
            $this->json(['error' => 'Невалидна заявка'], 400);
            return;
        }
        
        try {
            $currentUser = $this->app->getCurrentUser();
            $settings = UserNextcloudSettings::getActiveForUser($currentUser['id']);
            
            if (!$settings) {
                $this->json(['error' => 'Няма настройки за изтриване'], 400);
                return;
            }
            
            if ($settings->deleteSettings()) {
                $this->json(['success' => true, 'message' => 'Nextcloud настройките са изтрити']);
            } else {
                $this->json(['error' => 'Грешка при изтриване на настройките'], 500);
            }
            
        } catch (Exception $e) {
            $this->json(['error' => 'Грешка при изтриване: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Get Nextcloud settings as JSON
     */
    public function getNextcloudSettings(): void
    {
        $this->requireAuth();
        $this->requirePermission('nextcloud.view_personal');
        
        try {
            $currentUser = $this->app->getCurrentUser();
            $settings = UserNextcloudSettings::getActiveForUser($currentUser['id']);
            
            if (!$settings) {
                $this->json(['settings' => null]);
                return;
            }
            
            // Return settings without sensitive data
            $safeSettings = [
                'id' => $settings->id,
                'server_url' => $settings->server_url,
                'username' => $settings->username,
                'verify_ssl' => $settings->verify_ssl,
                'timeout' => $settings->timeout,
                'default_folder' => $settings->default_folder,
                'auto_create_folders' => $settings->auto_create_folders,
                'last_tested_at' => $settings->last_tested_at,
                'last_test_result' => $settings->last_test_result,
                'last_error_message' => $settings->last_error_message,
                'has_password' => !empty($settings->password)
            ];
            
            $this->json(['settings' => $safeSettings]);
            
        } catch (Exception $e) {
            $this->json(['error' => 'Грешка при зареждане: ' . $e->getMessage()], 500);
        }
    }
}
