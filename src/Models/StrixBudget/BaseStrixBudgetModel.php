<?php

namespace Strix\ERP\Models\StrixBudget;

use Strix\ERP\Services\StrixBudgetClient;
use Exception;

abstract class BaseStrixBudgetModel
{
    protected array $attributes = [];
    protected array $fillable = [];
    protected string $endpoint;
    protected static ?StrixBudgetClient $client = null;
    
    public function __construct($attributes = [])
    {
        // Handle different input types
        if (is_array($attributes)) {
            $this->fill($attributes);
        } elseif (is_int($attributes)) {
            // If an ID is passed, try to load the model
            $this->attributes['id'] = $attributes;
        } elseif (is_object($attributes)) {
            // Convert object to array
            $this->fill((array) $attributes);
        } else {
            // Default to empty array
            $this->fill([]);
        }
    }
    
    /**
     * Set StrixBudget client instance
     */
    public static function setClient(StrixBudgetClient $client): void
    {
        self::$client = $client;
    }
    
    /**
     * Get StrixBudget client instance
     */
    protected static function getClient(): StrixBudgetClient
    {
        if (self::$client === null) {
            throw new Exception('StrixBudget client not configured');
        }
        
        return self::$client;
    }
    
    /**
     * Magic getter for attributes
     */
    public function __get(string $name)
    {
        return $this->attributes[$name] ?? null;
    }
    
    /**
     * Magic setter for attributes
     */
    public function __set(string $name, $value): void
    {
        $this->attributes[$name] = $value;
    }
    
    /**
     * Check if attribute exists
     */
    public function __isset(string $name): bool
    {
        return isset($this->attributes[$name]);
    }
    
    /**
     * Fill model with data
     */
    public function fill(array $data): void
    {
        foreach ($data as $key => $value) {
            if (empty($this->fillable) || in_array($key, $this->fillable)) {
                $this->attributes[$key] = $value;
            }
        }
    }
    
    /**
     * Get all attributes
     */
    public function getAttributes(): array
    {
        return $this->attributes;
    }
    
    /**
     * Get fillable attributes only
     */
    public function getFillableAttributes(): array
    {
        if (empty($this->fillable)) {
            return $this->attributes;
        }
        
        return array_intersect_key($this->attributes, array_flip($this->fillable));
    }
    
    /**
     * Convert model to array
     */
    public function toArray(): array
    {
        return $this->attributes;
    }
    
    /**
     * Convert model to JSON
     */
    public function toJson(): string
    {
        return json_encode($this->attributes);
    }
    
    /**
     * Find model by ID
     */
    public static function find(int $id): ?static
    {
        try {
            $instance = new static([]);
            $method = 'get' . ucfirst($instance->getResourceName());

            $response = self::getClient()->$method($id);

            if ($response['success'] && isset($response['data'])) {
                return new static($response['data']);
            }

            return null;
        } catch (Exception $e) {
            error_log("Error finding " . static::class . " with ID $id: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get all models
     */
    public static function all(array $filters = []): array
    {
        try {
            $instance = new static([]);
            $resourceName = $instance->getResourceName();

            // Handle special pluralization cases
            $pluralMap = [
                'counterparty' => 'counterparties',
                'transactionType' => 'transactionTypes',
                'bankAccount' => 'bankAccounts'
            ];

            $plural = $pluralMap[$resourceName] ?? $resourceName . 's';
            $method = 'get' . ucfirst($plural);

            $response = self::getClient()->$method($filters);

            if ($response['success'] && isset($response['data'])) {
                $models = [];

                // Handle different response structures
                $itemData = [];
                if (isset($response['data']['data'])) {
                    // Paginated API response
                    $itemData = $response['data']['data'];
                } elseif (is_array($response['data']) && !isset($response['data']['current_page'])) {
                    // Simple array response
                    $itemData = $response['data'];
                } else {
                    error_log("Unexpected data structure in " . static::class . "::all(): " . print_r($response['data'], true));
                    return [];
                }

                foreach ($itemData as $item) {
                    if (is_array($item)) {
                        $models[] = new static($item);
                    }
                }
                return $models;
            }

            return [];
        } catch (Exception $e) {
            error_log("Error getting all " . static::class . " (method: $method): " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Save model (create or update)
     */
    public function save(): bool
    {
        try {
            $data = $this->getFillableAttributes();
            
            if (isset($this->attributes['id'])) {
                // Update existing
                $method = 'update' . ucfirst($this->getResourceName());
                $response = self::getClient()->$method($this->attributes['id'], $data);
            } else {
                // Create new
                $method = 'create' . ucfirst($this->getResourceName());
                $response = self::getClient()->$method($data);
            }
            
            if ($response['success'] && isset($response['data'])) {
                $this->fill($response['data']);
                return true;
            }
            
            return false;
        } catch (Exception $e) {
            error_log("Error saving " . static::class . ": " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete model
     */
    public function delete(): bool
    {
        try {
            if (!isset($this->attributes['id'])) {
                return false;
            }
            
            $method = 'delete' . ucfirst($this->getResourceName());
            $response = self::getClient()->$method($this->attributes['id']);
            
            return $response['success'] ?? false;
        } catch (Exception $e) {
            error_log("Error deleting " . static::class . ": " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create new model
     */
    public static function create(array $data): ?static
    {
        $instance = new static($data);

        if ($instance->save()) {
            return $instance;
        }

        return null;
    }
    
    /**
     * Get resource name for API calls
     */
    abstract protected function getResourceName(): string;
    
    /**
     * Get validation errors from last API call
     */
    public function getErrors(): array
    {
        // This would need to be implemented to store errors from API responses
        return [];
    }
}
