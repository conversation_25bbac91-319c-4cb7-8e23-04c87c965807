<?php

namespace Strix\ERP\Models\StrixBudget;

class BankAccount extends BaseStrixBudgetModel
{
    protected array $fillable = [
        'id',
        'user_id',
        'name',
        'number',
        'bank_name',
        'currency',
        'balance',
        'description',
        'is_active',
        'is_default',
        'transactions_count',
        'total_income',
        'total_expenses',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    
    protected function getResourceName(): string
    {
        return 'bankAccount';
    }
    
    /**
     * Get bank account statistics
     */
    public function getStatistics(): array
    {
        try {
            if (!isset($this->attributes['id'])) {
                return [];
            }
            
            $response = self::getClient()->getBankAccountStatistics($this->attributes['id']);
            
            if ($response['success'] && isset($response['data'])) {
                return $response['data'];
            }
            
            return [];
        } catch (\Exception $e) {
            error_log("Error getting bank account statistics: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get formatted balance
     */
    public function getFormattedBalance(): string
    {
        $balance = $this->attributes['balance'] ?? 0;
        $currency = $this->attributes['currency'] ?? 'EUR';
        
        return number_format($balance, 2) . ' ' . $currency;
    }
    
    /**
     * Check if account is active
     */
    public function isActive(): bool
    {
        return (bool) ($this->attributes['is_active'] ?? false);
    }
    
    /**
     * Check if account is default
     */
    public function isDefault(): bool
    {
        return (bool) ($this->attributes['is_default'] ?? false);
    }
    
    /**
     * Get account name with currency
     */
    public function getDisplayName(): string
    {
        $name = $this->attributes['name'] ?? 'Unknown Account';
        $currency = $this->attributes['currency'] ?? '';
        
        return $currency ? "$name ($currency)" : $name;
    }
    
    /**
     * Get all active bank accounts
     */
    public static function getActive(): array
    {
        $accounts = self::all();
        return array_filter($accounts, function($account) {
            return $account->isActive();
        });
    }
    
    /**
     * Get default bank account
     */
    public static function getDefault(): ?self
    {
        $accounts = self::all();
        foreach ($accounts as $account) {
            if ($account->isDefault()) {
                return $account;
            }
        }
        return null;
    }
    
    /**
     * Get accounts by currency
     */
    public static function getByCurrency(string $currency): array
    {
        $accounts = self::all();
        return array_filter($accounts, function($account) use ($currency) {
            return $account->attributes['currency'] === $currency;
        });
    }
}
