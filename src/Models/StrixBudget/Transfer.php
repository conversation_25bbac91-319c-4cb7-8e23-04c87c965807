<?php

namespace Strix\ERP\Models\StrixBudget;

class Transfer extends BaseStrixBudgetModel
{
    protected array $fillable = [
        'id',
        'user_id',
        'from_account_id',
        'to_account_id',
        'amount_from',
        'currency_from',
        'amount_to',
        'currency_to',
        'exchange_rate',
        'description',
        'executed_at',
        'created_at',
        'updated_at',
        'deleted_at',
        'from_account',
        'to_account'
    ];
    
    protected function getResourceName(): string
    {
        return 'transfer';
    }
    
    /**
     * Get formatted amount from
     */
    public function getFormattedAmountFrom(): string
    {
        $amount = $this->attributes['amount_from'] ?? 0;
        $currency = $this->attributes['currency_from'] ?? 'EUR';
        
        return number_format($amount, 2) . ' ' . $currency;
    }
    
    /**
     * Get formatted amount to
     */
    public function getFormattedAmountTo(): string
    {
        $amount = $this->attributes['amount_to'] ?? 0;
        $currency = $this->attributes['currency_to'] ?? 'EUR';
        
        return number_format($amount, 2) . ' ' . $currency;
    }
    
    /**
     * Get formatted exchange rate
     */
    public function getFormattedExchangeRate(): string
    {
        $rate = $this->attributes['exchange_rate'] ?? 1;
        $currencyFrom = $this->attributes['currency_from'] ?? 'EUR';
        $currencyTo = $this->attributes['currency_to'] ?? 'EUR';
        
        return "1 {$currencyFrom} = " . number_format($rate, 4) . " {$currencyTo}";
    }
    
    /**
     * Get formatted date
     */
    public function getFormattedDate(): string
    {
        $date = $this->attributes['executed_at'] ?? '';
        if ($date) {
            return date('d.m.Y', strtotime($date));
        }
        return '';
    }
    
    /**
     * Get formatted datetime
     */
    public function getFormattedDateTime(): string
    {
        $date = $this->attributes['executed_at'] ?? '';
        if ($date) {
            return date('d.m.Y H:i', strtotime($date));
        }
        return '';
    }
    
    /**
     * Check if this is a currency exchange
     */
    public function isCurrencyExchange(): bool
    {
        $currencyFrom = $this->attributes['currency_from'] ?? '';
        $currencyTo = $this->attributes['currency_to'] ?? '';
        
        return $currencyFrom !== $currencyTo;
    }
    
    /**
     * Get transfer summary
     */
    public function getSummary(): string
    {
        $fromAmount = $this->getFormattedAmountFrom();
        $toAmount = $this->getFormattedAmountTo();
        
        if ($this->isCurrencyExchange()) {
            return "{$fromAmount} → {$toAmount}";
        } else {
            return $fromAmount;
        }
    }
    
    /**
     * Get transfer direction icon
     */
    public function getDirectionIcon(): string
    {
        return '🔄';
    }
    
    /**
     * Get transfers by account (either from or to)
     */
    public static function getByAccount(int $accountId): array
    {
        $allTransfers = self::all();
        return array_filter($allTransfers, function($transfer) use ($accountId) {
            return $transfer->attributes['from_account_id'] == $accountId || 
                   $transfer->attributes['to_account_id'] == $accountId;
        });
    }
    
    /**
     * Get transfers from specific account
     */
    public static function getFromAccount(int $accountId): array
    {
        $allTransfers = self::all();
        return array_filter($allTransfers, function($transfer) use ($accountId) {
            return $transfer->attributes['from_account_id'] == $accountId;
        });
    }
    
    /**
     * Get transfers to specific account
     */
    public static function getToAccount(int $accountId): array
    {
        $allTransfers = self::all();
        return array_filter($allTransfers, function($transfer) use ($accountId) {
            return $transfer->attributes['to_account_id'] == $accountId;
        });
    }
    
    /**
     * Get currency exchange transfers
     */
    public static function getCurrencyExchanges(): array
    {
        $allTransfers = self::all();
        return array_filter($allTransfers, function($transfer) {
            return $transfer->isCurrencyExchange();
        });
    }
    
    /**
     * Get same currency transfers
     */
    public static function getSameCurrencyTransfers(): array
    {
        $allTransfers = self::all();
        return array_filter($allTransfers, function($transfer) {
            return !$transfer->isCurrencyExchange();
        });
    }
}
