<?php

namespace Strix\ERP\Models;

use Strix\ERP\Core\Model;
use Strix\ERP\Core\Database;

class TaskAttachment extends Model
{
    protected string $table = 'task_attachments';
    
    protected array $fillable = [
        'task_id', 'user_id', 'original_name', 'stored_name', 
        'file_path', 'file_size', 'mime_type'
    ];
    
    protected array $casts = [
        'file_size' => 'integer'
    ];

    public function getTask(): ?Task
    {
        if (!$this->task_id) {
            return null;
        }
        return Task::find($this->task_id);
    }

    public function getUser(): ?User
    {
        if (!$this->user_id) {
            return null;
        }
        return User::find($this->user_id);
    }

    public function getUserName(): string
    {
        $user = $this->getUser();
        return $user ? $user->getFullName() : 'Неизвестен потребител';
    }

    public function getFormattedFileSize(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    public function getFileExtension(): string
    {
        return strtolower(pathinfo($this->original_name, PATHINFO_EXTENSION));
    }

    public function isImage(): bool
    {
        $imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        return in_array($this->getFileExtension(), $imageTypes);
    }

    public function isDocument(): bool
    {
        $docTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf'];
        return in_array($this->getFileExtension(), $docTypes);
    }

    public function getFileIcon(): string
    {
        $extension = $this->getFileExtension();
        
        if ($this->isImage()) {
            return 'image';
        }
        
        switch ($extension) {
            case 'pdf':
                return 'file-pdf';
            case 'doc':
            case 'docx':
                return 'file-word';
            case 'xls':
            case 'xlsx':
                return 'file-excel';
            case 'ppt':
            case 'pptx':
                return 'file-powerpoint';
            case 'zip':
            case 'rar':
            case '7z':
                return 'file-archive';
            case 'txt':
                return 'file-text';
            default:
                return 'file';
        }
    }

    public function getDownloadUrl(): string
    {
        return "/admin/tasks/attachments/{$this->id}/download";
    }

    public static function getAttachmentsForTask(int $taskId): array
    {
        $results = Database::fetchAll(
            "SELECT ta.*, u.username, u.first_name, u.last_name
             FROM task_attachments ta
             INNER JOIN users u ON ta.user_id = u.id
             WHERE ta.task_id = ?
             ORDER BY ta.created_at",
            [$taskId]
        );
        
        $attachments = [];
        foreach ($results as $data) {
            $attachment = new self();
            foreach ($data as $key => $value) {
                $attachment->$key = $value;
            }
            $attachments[] = $attachment;
        }
        
        return $attachments;
    }

    public function delete(): bool
    {
        // Delete physical file
        if (file_exists($this->file_path)) {
            unlink($this->file_path);
        }
        
        // Delete database record
        return parent::delete();
    }
}
