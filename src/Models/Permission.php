<?php

namespace Strix\ERP\Models;

use Strix\ERP\Core\Model;
use Strix\ERP\Core\Database;

class Permission extends Model
{
    protected string $table = 'permissions';
    
    protected array $fillable = [
        'name', 'description', 'module', 'action'
    ];

    public static function findByName(string $name): ?self
    {
        return self::findBy('name', $name);
    }

    public function getGroups(): array
    {
        $results = Database::fetchAll(
            "SELECT g.* FROM groups g
             INNER JOIN group_permissions gp ON g.id = gp.group_id
             WHERE gp.permission_id = ? AND g.is_active = 1",
            [$this->id]
        );
        
        $groups = [];
        foreach ($results as $data) {
            $group = new Group();
            foreach ($data as $key => $value) {
                $group->$key = $value;
            }
            $groups[] = $group;
        }
        return $groups;
    }

    public function getUsers(): array
    {
        $results = Database::fetchAll(
            "SELECT DISTINCT u.* FROM users u
             INNER JOIN user_groups ug ON u.id = ug.user_id
             INNER JOIN group_permissions gp ON ug.group_id = gp.group_id
             WHERE gp.permission_id = ? AND u.is_active = 1",
            [$this->id]
        );
        
        $users = [];
        foreach ($results as $data) {
            $user = new User();
            foreach ($data as $key => $value) {
                $user->$key = $value;
            }
            $users[] = $user;
        }
        return $users;
    }

    public static function getByModule(string $module): array
    {
        return self::where('module', $module);
    }

    public static function getAllModules(): array
    {
        $results = Database::fetchAll(
            "SELECT DISTINCT module FROM permissions ORDER BY module"
        );
        
        return array_column($results, 'module');
    }

    public static function getGroupedByModule(): array
    {
        $permissions = self::all();
        $grouped = [];
        
        foreach ($permissions as $permission) {
            $grouped[$permission->module][] = $permission;
        }
        
        return $grouped;
    }

    public static function searchPermissions(string $query): array
    {
        $results = Database::fetchAll(
            "SELECT * FROM permissions 
             WHERE (name LIKE ? OR description LIKE ? OR module LIKE ? OR action LIKE ?)
             ORDER BY module, name",
            ["%$query%", "%$query%", "%$query%", "%$query%"]
        );
        
        $permissions = [];
        foreach ($results as $data) {
            $permission = new self();
            foreach ($data as $key => $value) {
                $permission->$key = $value;
            }
            $permissions[] = $permission;
        }
        return $permissions;
    }

    public function getFullName(): string
    {
        return $this->module . '.' . $this->action;
    }
}
