<?php

namespace Strix\ERP\Models;

use Strix\ERP\Core\Model;
use Strix\ERP\Core\Database;

class TaskType extends Model
{
    protected string $table = 'task_types';
    
    protected array $fillable = [
        'name', 'description', 'color', 'icon', 'is_active'
    ];
    
    protected array $casts = [
        'is_active' => 'boolean'
    ];

    public static function findByName(string $name): ?self
    {
        return self::findBy('name', $name);
    }

    public function getTasks(): array
    {
        return Task::where('task_type_id', $this->id);
    }

    public function getTaskCount(): int
    {
        return (int) Database::fetchColumn(
            "SELECT COUNT(*) FROM tasks WHERE task_type_id = ?",
            [$this->id]
        );
    }

    public function getActiveTaskCount(): int
    {
        return (int) Database::fetchColumn(
            "SELECT COUNT(*) FROM tasks t
             INNER JOIN task_statuses ts ON t.status_id = ts.id
             WHERE t.task_type_id = ? AND ts.is_final = FALSE",
            [$this->id]
        );
    }

    public function isActive(): bool
    {
        return (bool) $this->is_active;
    }

    public static function getActiveTypes(): array
    {
        return self::where('is_active', 1);
    }

    public static function getAllWithStats(): array
    {
        $results = Database::fetchAll(
            "SELECT tt.*, 
                    COUNT(t.id) as total_tasks,
                    COUNT(CASE WHEN ts.is_final = FALSE THEN 1 END) as active_tasks
             FROM task_types tt
             LEFT JOIN tasks t ON tt.id = t.task_type_id
             LEFT JOIN task_statuses ts ON t.status_id = ts.id
             GROUP BY tt.id
             ORDER BY tt.name"
        );
        
        $types = [];
        foreach ($results as $data) {
            $type = new self();
            foreach ($data as $key => $value) {
                $type->$key = $value;
            }
            $types[] = $type;
        }
        
        return $types;
    }
}
