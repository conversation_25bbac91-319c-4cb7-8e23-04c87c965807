<?php

namespace Strix\ERP\Models;

use Strix\ERP\Core\Model;
use Strix\ERP\Core\Database;

class Task extends Model
{
    protected string $table = 'tasks';
    
    protected array $fillable = [
        'title', 'description', 'task_type_id', 'status_id', 'priority',
        'progress', 'due_date', 'start_date', 'created_by', 'assigned_to',
        'estimated_hours', 'actual_hours'
    ];
    
    protected array $casts = [
        'progress' => 'integer',
        'estimated_hours' => 'float',
        'actual_hours' => 'float'
    ];

    public function getCreator(): ?User
    {
        if (!$this->created_by) {
            return null;
        }
        return User::find($this->created_by);
    }

    public function getPrimaryAssignee(): ?User
    {
        if (!$this->assigned_to) {
            return null;
        }
        return User::find($this->assigned_to);
    }

    public function getTaskType(): ?TaskType
    {
        if (!$this->task_type_id) {
            return null;
        }
        return TaskType::find($this->task_type_id);
    }

    public function getTaskStatus(): ?TaskStatus
    {
        if (!$this->status_id) {
            return null;
        }
        return TaskStatus::find($this->status_id);
    }

    public function getStatus(): ?TaskStatus
    {
        if (!$this->status_id) {
            return null;
        }
        return TaskStatus::find($this->status_id);
    }

    public function getAssignees(): array
    {
        $results = Database::fetchAll(
            "SELECT ta.*, u.username, u.first_name, u.last_name, u.email,
                    g.name as group_name, g.description as group_description
             FROM task_assignees ta
             LEFT JOIN users u ON ta.user_id = u.id
             LEFT JOIN groups g ON ta.group_id = g.id
             WHERE ta.task_id = ?
             ORDER BY ta.assigned_at",
            [$this->id]
        );
        
        $assignees = [];
        foreach ($results as $data) {
            if ($data['user_id']) {
                $assignees[] = [
                    'type' => 'user',
                    'id' => $data['user_id'],
                    'name' => trim($data['first_name'] . ' ' . $data['last_name']),
                    'username' => $data['username'],
                    'email' => $data['email'],
                    'assigned_at' => $data['assigned_at']
                ];
            } elseif ($data['group_id']) {
                $assignees[] = [
                    'type' => 'group',
                    'id' => $data['group_id'],
                    'name' => $data['group_name'],
                    'description' => $data['group_description'],
                    'assigned_at' => $data['assigned_at']
                ];
            }
        }
        
        return $assignees;
    }

    public function getComments(): array
    {
        $results = Database::fetchAll(
            "SELECT tc.*, u.username, u.first_name, u.last_name
             FROM task_comments tc
             INNER JOIN users u ON tc.user_id = u.id
             WHERE tc.task_id = ?
             ORDER BY tc.created_at",
            [$this->id]
        );
        
        $comments = [];
        foreach ($results as $data) {
            $comment = new TaskComment();
            foreach ($data as $key => $value) {
                $comment->$key = $value;
            }
            $comments[] = $comment;
        }
        
        return $comments;
    }

    public function getAttachments(): array
    {
        $results = Database::fetchAll(
            "SELECT ta.*, u.username, u.first_name, u.last_name
             FROM task_attachments ta
             INNER JOIN users u ON ta.user_id = u.id
             WHERE ta.task_id = ?
             ORDER BY ta.created_at",
            [$this->id]
        );
        
        $attachments = [];
        foreach ($results as $data) {
            $attachment = new TaskAttachment();
            foreach ($data as $key => $value) {
                $attachment->$key = $value;
            }
            $attachments[] = $attachment;
        }
        
        return $attachments;
    }

    public function assignUser(int $userId, int $assignedBy): bool
    {
        try {
            Database::insert('task_assignees', [
                'task_id' => $this->id,
                'user_id' => $userId,
                'assigned_by' => $assignedBy
            ]);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function assignGroup(int $groupId, int $assignedBy): bool
    {
        try {
            Database::insert('task_assignees', [
                'task_id' => $this->id,
                'group_id' => $groupId,
                'assigned_by' => $assignedBy
            ]);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function removeAssignee(int $userId = null, int $groupId = null): bool
    {
        $where = ['task_id' => $this->id];
        
        if ($userId) {
            $where['user_id'] = $userId;
        } elseif ($groupId) {
            $where['group_id'] = $groupId;
        } else {
            return false;
        }
        
        return Database::delete('task_assignees', $where) > 0;
    }

    public function addComment(int $userId, string $comment, bool $isInternal = false): ?TaskComment
    {
        try {
            $id = Database::insert('task_comments', [
                'task_id' => $this->id,
                'user_id' => $userId,
                'comment' => $comment,
                'is_internal' => $isInternal ? 1 : 0
            ]);
            
            return TaskComment::find($id);
        } catch (\Exception $e) {
            return null;
        }
    }

    public function updateProgress(int $progress): bool
    {
        if ($progress < 0 || $progress > 100) {
            return false;
        }
        
        $this->progress = $progress;
        
        // Auto-complete task if progress is 100%
        if ($progress === 100) {
            $completedStatus = TaskStatus::findByName('Завършена');
            if ($completedStatus) {
                $this->status_id = $completedStatus->id;
                $this->completed_at = date('Y-m-d H:i:s');
            }
        }
        
        return $this->save();
    }

    public function updateStatus(int $statusId): bool
    {
        $status = TaskStatus::find($statusId);
        if (!$status) {
            return false;
        }
        
        $this->status_id = $statusId;
        
        // Set completed_at if status is final
        if ($status->is_final) {
            $this->completed_at = date('Y-m-d H:i:s');
            if ($status->name === 'Завършена') {
                $this->progress = 100;
            }
        } else {
            $this->completed_at = null;
        }
        
        return $this->save();
    }

    public function isOverdue(): bool
    {
        if (!$this->due_date) {
            return false;
        }
        
        $status = $this->getStatus();
        if ($status && $status->is_final) {
            return false; // Completed tasks are not overdue
        }
        
        return strtotime($this->due_date) < time();
    }

    public function getDaysUntilDue(): ?int
    {
        if (!$this->due_date) {
            return null;
        }
        
        $dueTimestamp = strtotime($this->due_date);
        $nowTimestamp = strtotime(date('Y-m-d'));
        
        return (int) (($dueTimestamp - $nowTimestamp) / (24 * 60 * 60));
    }

    public function getPriorityLabel(): string
    {
        switch ($this->priority) {
            case 'urgent':
                return 'Спешно';
            case 'high':
                return 'Висок';
            case 'normal':
                return 'Нормален';
            case 'low':
                return 'Нисък';
            default:
                return 'Неизвестен';
        }
    }

    public function getPriorityColor(): string
    {
        switch ($this->priority) {
            case 'urgent':
                return '#e74c3c';
            case 'high':
                return '#f39c12';
            case 'normal':
                return '#3498db';
            case 'low':
                return '#95a5a6';
            default:
                return '#95a5a6';
        }
    }

    public static function getTasksForUser(int $userId, array $filters = []): array
    {
        // Optimized query with JOINs to avoid N+1 problems
        $sql = "SELECT DISTINCT t.*,
                       tt.name as type_name, tt.color as type_color,
                       ts.name as status_name, ts.color as status_color, ts.is_final,
                       u1.first_name as creator_first_name, u1.last_name as creator_last_name,
                       u2.first_name as assignee_first_name, u2.last_name as assignee_last_name
                FROM tasks t
                INNER JOIN task_types tt ON t.task_type_id = tt.id
                INNER JOIN task_statuses ts ON t.status_id = ts.id
                INNER JOIN users u1 ON t.created_by = u1.id
                LEFT JOIN users u2 ON t.assigned_to = u2.id
                LEFT JOIN task_assignees ta ON t.id = ta.task_id
                LEFT JOIN user_groups ug ON ta.group_id = ug.group_id
                WHERE (t.assigned_to = ? OR ta.user_id = ? OR ug.user_id = ?)";

        $params = [$userId, $userId, $userId];
        
        // Add filters
        if (!empty($filters['status_id'])) {
            $sql .= " AND t.status_id = ?";
            $params[] = $filters['status_id'];
        }
        
        if (!empty($filters['priority'])) {
            $sql .= " AND t.priority = ?";
            $params[] = $filters['priority'];
        }
        
        if (!empty($filters['task_type_id'])) {
            $sql .= " AND t.task_type_id = ?";
            $params[] = $filters['task_type_id'];
        }
        
        $sql .= " ORDER BY t.created_at DESC";
        
        $results = Database::fetchAll($sql, $params);
        
        $tasks = [];
        foreach ($results as $data) {
            $task = new self();
            foreach ($data as $key => $value) {
                $task->$key = $value;
            }
            $tasks[] = $task;
        }
        
        return $tasks;
    }

    public static function getOverdueTasks(): array
    {
        // Optimized query with JOINs to get all related data
        $sql = "SELECT t.*,
                       tt.name as type_name, tt.color as type_color,
                       ts.name as status_name, ts.color as status_color,
                       u1.first_name as creator_first_name, u1.last_name as creator_last_name,
                       u2.first_name as assignee_first_name, u2.last_name as assignee_last_name
                FROM tasks t
                INNER JOIN task_types tt ON t.task_type_id = tt.id
                INNER JOIN task_statuses ts ON t.status_id = ts.id
                INNER JOIN users u1 ON t.created_by = u1.id
                LEFT JOIN users u2 ON t.assigned_to = u2.id
                WHERE t.due_date < CURDATE() AND ts.is_final = FALSE
                ORDER BY t.due_date";

        $results = Database::fetchAll($sql);
        
        $tasks = [];
        foreach ($results as $data) {
            $task = new self();
            foreach ($data as $key => $value) {
                $task->$key = $value;
            }
            $tasks[] = $task;
        }
        
        return $tasks;
    }

    public static function getTasksByStatus(int $statusId): array
    {
        return self::where('status_id', $statusId);
    }

    public static function getTasksByType(int $typeId): array
    {
        return self::where('task_type_id', $typeId);
    }

    public static function searchTasks(string $query): array
    {
        $results = Database::fetchAll(
            "SELECT * FROM tasks 
             WHERE title LIKE ? OR description LIKE ?
             ORDER BY created_at DESC",
            ["%$query%", "%$query%"]
        );
        
        $tasks = [];
        foreach ($results as $data) {
            $task = new self();
            foreach ($data as $key => $value) {
                $task->$key = $value;
            }
            $tasks[] = $task;
        }
        
        return $tasks;
    }
}
