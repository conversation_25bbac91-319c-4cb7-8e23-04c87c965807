<?php

namespace Strix\ERP\Models;

use Strix\ERP\Core\Model;
use Strix\ERP\Core\Database;

class User extends Model
{
    protected string $table = 'users';
    
    protected array $fillable = [
        'username', 'email', 'password_hash', 'first_name', 'last_name', 'is_active'
    ];
    
    protected array $hidden = [
        'password_hash'
    ];
    
    protected array $casts = [
        'is_active' => 'boolean',
        'failed_login_attempts' => 'integer'
    ];

    public static function findByUsername(string $username): ?self
    {
        return self::findBy('username', $username);
    }

    public static function findByEmail(string $email): ?self
    {
        return self::findBy('email', $email);
    }

    public function setPassword(string $password): void
    {
        $this->password_hash = password_hash($password, PASSWORD_DEFAULT);
    }

    public function verifyPassword(string $password): bool
    {
        return password_verify($password, $this->password_hash);
    }

    public function getFullName(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    public function getGroups(): array
    {
        $results = Database::fetchAll(
            "SELECT g.* FROM groups g 
             INNER JOIN user_groups ug ON g.id = ug.group_id 
             WHERE ug.user_id = ? AND g.is_active = 1",
            [$this->id]
        );
        
        $groups = [];
        foreach ($results as $data) {
            $group = new Group();
            foreach ($data as $key => $value) {
                $group->$key = $value;
            }
            $groups[] = $group;
        }
        return $groups;
    }

    public function getPermissions(): array
    {
        $results = Database::fetchAll(
            "SELECT DISTINCT p.* FROM permissions p
             INNER JOIN group_permissions gp ON p.id = gp.permission_id
             INNER JOIN user_groups ug ON gp.group_id = ug.group_id
             WHERE ug.user_id = ?",
            [$this->id]
        );
        
        $permissions = [];
        foreach ($results as $data) {
            $permission = new Permission();
            foreach ($data as $key => $value) {
                $permission->$key = $value;
            }
            $permissions[] = $permission;
        }
        return $permissions;
    }

    public function getPermissionNames(): array
    {
        $permissions = $this->getPermissions();
        return array_map(fn($permission) => $permission->name, $permissions);
    }

    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->getPermissionNames());
    }

    public function hasGroup(string $groupName): bool
    {
        $groups = $this->getGroups();
        foreach ($groups as $group) {
            if ($group->name === $groupName) {
                return true;
            }
        }
        return false;
    }

    public function addToGroup(int $groupId, ?int $assignedBy = null): bool
    {
        try {
            Database::insert('user_groups', [
                'user_id' => $this->id,
                'group_id' => $groupId,
                'assigned_by' => $assignedBy
            ]);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function removeFromGroup(int $groupId): bool
    {
        return Database::delete('user_groups', [
            'user_id' => $this->id,
            'group_id' => $groupId
        ]) > 0;
    }

    public function updateLastLogin(): void
    {
        Database::update('users', 
            ['last_login' => date('Y-m-d H:i:s')], 
            ['id' => $this->id]
        );
    }

    public function incrementFailedAttempts(): void
    {
        $attempts = ($this->failed_login_attempts ?? 0) + 1;
        Database::update('users', 
            ['failed_login_attempts' => $attempts], 
            ['id' => $this->id]
        );
        $this->failed_login_attempts = $attempts;
    }

    public function resetFailedAttempts(): void
    {
        Database::update('users', 
            ['failed_login_attempts' => 0, 'locked_until' => null], 
            ['id' => $this->id]
        );
        $this->failed_login_attempts = 0;
        $this->locked_until = null;
    }

    public function lockAccount(int $minutes = 15): void
    {
        $lockedUntil = date('Y-m-d H:i:s', time() + ($minutes * 60));
        Database::update('users', 
            ['locked_until' => $lockedUntil], 
            ['id' => $this->id]
        );
        $this->locked_until = $lockedUntil;
    }

    public function isLocked(): bool
    {
        if (!$this->locked_until) {
            return false;
        }
        
        return strtotime($this->locked_until) > time();
    }

    public function isActive(): bool
    {
        return (bool) $this->is_active;
    }

    public static function getActiveUsers(): array
    {
        return self::where('is_active', 1);
    }

    public static function searchUsers(string $query): array
    {
        $results = Database::fetchAll(
            "SELECT * FROM users 
             WHERE (username LIKE ? OR email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)
             ORDER BY first_name, last_name",
            ["%$query%", "%$query%", "%$query%", "%$query%"]
        );
        
        $users = [];
        foreach ($results as $data) {
            $user = new self();
            foreach ($data as $key => $value) {
                $user->$key = $value;
            }
            $users[] = $user;
        }
        return $users;
    }


}
