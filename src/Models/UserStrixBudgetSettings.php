<?php

namespace Strix\ERP\Models;

use Strix\ERP\Core\Model;
use Strix\ERP\Core\Database;

class UserStrixBudgetSettings extends Model
{
    protected string $table = 'user_strixbudget_settings';
    protected array $fillable = [
        'user_id',
        'api_url',
        'api_token',
        'api_email',
        'api_password',
        'auth_method',
        'is_active'
    ];
    
    /**
     * Get settings for specific user
     */
    public static function getByUserId(int $userId): ?self
    {
        $data = Database::fetchOne(
            "SELECT * FROM user_strixbudget_settings WHERE user_id = ?",
            [$userId]
        );
        
        if (!$data) {
            return null;
        }
        
        $instance = new self();
        foreach ($data as $key => $value) {
            $instance->attributes[$key] = $value;
        }
        
        return $instance;
    }
    
    /**
     * Get or create settings for user
     */
    public static function getOrCreateForUser(int $userId): self
    {
        $settings = self::getByUserId($userId);
        
        if (!$settings) {
            $settings = new self();
            $settings->user_id = $userId;
            $settings->is_active = false;
        }
        
        return $settings;
    }
    
    /**
     * Get decrypted API token
     */
    public function getDecryptedToken(): ?string
    {
        if (empty($this->attributes['api_token'])) {
            return null;
        }
        
        // Simple base64 decoding for now - in production use proper encryption
        $decoded = base64_decode($this->attributes['api_token']);
        return $decoded !== false ? $decoded : null;
    }
    
    /**
     * Set encrypted API token
     */
    public function setEncryptedToken(string $token): void
    {
        // Simple base64 encoding for now - in production use proper encryption
        $this->attributes['api_token'] = base64_encode($token);
    }

    /**
     * Get decrypted API email
     */
    public function getDecryptedEmail(): ?string
    {
        if (empty($this->attributes['api_email'])) {
            return null;
        }

        // Simple base64 decoding for now - in production use proper encryption
        $decoded = base64_decode($this->attributes['api_email']);
        return $decoded !== false ? $decoded : null;
    }

    /**
     * Set encrypted API email
     */
    public function setEncryptedEmail(string $email): void
    {
        // Simple base64 encoding for now - in production use proper encryption
        $this->attributes['api_email'] = base64_encode($email);
    }

    /**
     * Get decrypted API password
     */
    public function getDecryptedPassword(): ?string
    {
        if (empty($this->attributes['api_password'])) {
            return null;
        }

        // Simple base64 decoding for now - in production use proper encryption
        $decoded = base64_decode($this->attributes['api_password']);
        return $decoded !== false ? $decoded : null;
    }

    /**
     * Set encrypted API password
     */
    public function setEncryptedPassword(string $password): void
    {
        // Simple base64 encoding for now - in production use proper encryption
        $this->attributes['api_password'] = base64_encode($password);
    }
    
    /**
     * Get client configuration array
     */
    public function getClientConfig(): array
    {
        return [
            'api_url' => $this->attributes['api_url'] ?? '',
            'api_token' => $this->getDecryptedToken(),
            'api_email' => $this->getDecryptedEmail(),
            'api_password' => $this->getDecryptedPassword(),
            'auth_method' => $this->attributes['auth_method'] ?? 'token',
            'is_active' => (bool) ($this->attributes['is_active'] ?? false)
        ];
    }

    /**
     * Check if settings are configured and active
     */
    public function isConfigured(): bool
    {
        $hasUrl = !empty($this->attributes['api_url']);
        $isActive = (bool) ($this->attributes['is_active'] ?? false);
        $authMethod = $this->attributes['auth_method'] ?? 'token';

        if (!$hasUrl || !$isActive) {
            return false;
        }

        if ($authMethod === 'credentials') {
            return !empty($this->attributes['api_email']) && !empty($this->attributes['api_password']);
        } else {
            return !empty($this->attributes['api_token']);
        }
    }

    /**
     * Get authentication method
     */
    public function getAuthMethod(): string
    {
        return $this->attributes['auth_method'] ?? 'token';
    }

    /**
     * Check if using credentials authentication
     */
    public function usesCredentials(): bool
    {
        return $this->getAuthMethod() === 'credentials';
    }

    /**
     * Check if using token authentication
     */
    public function usesToken(): bool
    {
        return $this->getAuthMethod() === 'token';
    }
    
    /**
     * Check if settings are active
     */
    public function isActive(): bool
    {
        return (bool) ($this->attributes['is_active'] ?? false);
    }
    
    /**
     * Activate settings
     */
    public function activate(): bool
    {
        $this->is_active = true;
        return $this->save();
    }
    
    /**
     * Deactivate settings
     */
    public function deactivate(): bool
    {
        $this->is_active = false;
        return $this->save();
    }
    
    /**
     * Get masked token for display
     */
    public function getMaskedToken(): string
    {
        $token = $this->getDecryptedToken();
        if (empty($token)) {
            return 'Не е настроен';
        }

        if (strlen($token) <= 10) {
            return str_repeat('*', strlen($token));
        }

        return substr($token, 0, 5) . str_repeat('*', strlen($token) - 10) . substr($token, -5);
    }

    /**
     * Get masked email for display
     */
    public function getMaskedEmail(): string
    {
        $email = $this->getDecryptedEmail();
        if (empty($email)) {
            return 'Не е настроен';
        }

        $parts = explode('@', $email);
        if (count($parts) !== 2) {
            return str_repeat('*', strlen($email));
        }

        $username = $parts[0];
        $domain = $parts[1];

        if (strlen($username) <= 2) {
            $maskedUsername = str_repeat('*', strlen($username));
        } else {
            $maskedUsername = substr($username, 0, 1) . str_repeat('*', strlen($username) - 2) . substr($username, -1);
        }

        return $maskedUsername . '@' . $domain;
    }

    /**
     * Get masked password for display
     */
    public function getMaskedPassword(): string
    {
        $password = $this->getDecryptedPassword();
        if (empty($password)) {
            return 'Не е настроена';
        }

        return str_repeat('*', strlen($password));
    }
    
    /**
     * Validate API URL format
     */
    public function validateApiUrl(string $url): bool
    {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
    
    /**
     * Get all active user settings
     */
    public static function getActiveSettings(): array
    {
        $results = Database::fetchAll(
            "SELECT * FROM user_strixbudget_settings WHERE is_active = 1"
        );
        
        $settings = [];
        foreach ($results as $data) {
            $instance = new self();
            foreach ($data as $key => $value) {
                $instance->attributes[$key] = $value;
            }
            $settings[] = $instance;
        }
        
        return $settings;
    }
    
    /**
     * Delete settings for user
     */
    public static function deleteForUser(int $userId): bool
    {
        return Database::delete('user_strixbudget_settings', ['user_id' => $userId]) > 0;
    }
    
    /**
     * Hook called before saving the model
     */
    protected function beforeSave(): void
    {
        // Set timestamps
        if (!isset($this->attributes['id'])) {
            $this->attributes['created_at'] = date('Y-m-d H:i:s');
        }
        $this->attributes['updated_at'] = date('Y-m-d H:i:s');
    }
}
