<?php

namespace Strix\ERP\Services;

use Exception;
use Strix\ERP\Models\UserNextcloudSettings;

class NextcloudClient
{
    private array $config;
    private string $baseUrl;
    private string $webdavUrl;
    private array $authHeaders;
    
    public function __construct(?UserNextcloudSettings $userSettings = null)
    {
        if ($userSettings && $userSettings->server_url && $userSettings->username) {
            // Use user-specific settings
            $this->config = $userSettings->getClientConfig();
            $this->baseUrl = rtrim($userSettings->server_url, '/');
            $this->webdavUrl = $this->baseUrl . '/remote.php/dav/files/';

            $this->setupUserAuthentication($userSettings);
        } else {
            // Fallback to global config
            $this->config = require __DIR__ . '/../../config/nextcloud.php';
            $this->baseUrl = rtrim($this->config['server']['url'], '/');
            $this->webdavUrl = $this->baseUrl . $this->config['server']['webdav_path'];

            $this->setupAuthentication();
        }
    }
    
    private function setupAuthentication(): void
    {
        $username = $this->config['auth']['username'];
        $password = $this->config['auth']['password'];

        if (empty($username) || empty($password)) {
            throw new Exception('Nextcloud credentials not configured');
        }

        $this->authHeaders = [
            'Authorization: Basic ' . base64_encode($username . ':' . $password)
        ];
    }

    private function setupUserAuthentication(UserNextcloudSettings $userSettings): void
    {
        $username = $userSettings->username;
        $password = $userSettings->getDecryptedPassword();

        if (empty($username) || empty($password)) {
            throw new Exception('User Nextcloud credentials not configured');
        }

        $this->authHeaders = [
            'Authorization: Basic ' . base64_encode($username . ':' . $password)
        ];
    }
    
    /**
     * List files and folders in a directory
     */
    public function listDirectory(string $path = '/', string $username = null): array
    {
        $username = $username ?: $this->config['auth']['username'];
        $url = $this->webdavUrl . $username . '/' . ltrim($path, '/');
        
        $xml = '<?xml version="1.0"?>
        <d:propfind xmlns:d="DAV:" xmlns:oc="http://owncloud.org/ns" xmlns:nc="http://nextcloud.org/ns">
            <d:prop>
                <d:displayname />
                <d:getcontentlength />
                <d:getcontenttype />
                <d:getlastmodified />
                <d:resourcetype />
                <oc:size />
                <oc:permissions />
                <nc:has-preview />
            </d:prop>
        </d:propfind>';
        
        $response = $this->makeRequest('PROPFIND', $url, [
            'Content-Type: application/xml',
            'Depth: 1'
        ], $xml);
        
        return $this->parseDirectoryListing($response);
    }
    
    /**
     * Upload a file to Nextcloud
     */
    public function uploadFile(string $localPath, string $remotePath, string $username = null): bool
    {
        $username = $username ?: $this->config['auth']['username'];
        $url = $this->webdavUrl . $username . '/' . ltrim($remotePath, '/');
        
        if (!file_exists($localPath)) {
            throw new Exception("Local file not found: $localPath");
        }
        
        $fileHandle = fopen($localPath, 'r');
        if (!$fileHandle) {
            throw new Exception("Cannot open file: $localPath");
        }
        
        try {
            $response = $this->makeRequest('PUT', $url, [
                'Content-Type: application/octet-stream'
            ], $fileHandle);
            
            return $response !== false;
        } finally {
            fclose($fileHandle);
        }
    }
    
    /**
     * Upload file from string content
     */
    public function uploadContent(string $content, string $remotePath, string $username = null): bool
    {
        $username = $username ?: $this->config['auth']['username'];
        $url = $this->webdavUrl . $username . '/' . ltrim($remotePath, '/');
        
        $response = $this->makeRequest('PUT', $url, [
            'Content-Type: application/octet-stream'
        ], $content);
        
        return $response !== false;
    }
    
    /**
     * Download a file from Nextcloud
     */
    public function downloadFile(string $remotePath, string $localPath = null, string $username = null): string|bool
    {
        $username = $username ?: $this->config['auth']['username'];
        $url = $this->webdavUrl . $username . '/' . ltrim($remotePath, '/');

        $this->log('debug', "Downloading file from URL: $url");

        $response = $this->makeRequest('GET', $url);

        if ($response === false) {
            $this->log('error', "Failed to download file: $remotePath");
            return false;
        }

        $this->log('debug', "Downloaded file successfully, size: " . strlen($response) . " bytes");

        if ($localPath) {
            $result = file_put_contents($localPath, $response);
            if ($result === false) {
                $this->log('error', "Failed to write file to local path: $localPath");
                return false;
            }
            return true;
        }

        return $response;
    }
    
    /**
     * Create a directory
     */
    public function createDirectory(string $path, string $username = null): bool
    {
        $username = $username ?: $this->config['auth']['username'];
        $url = $this->webdavUrl . $username . '/' . ltrim($path, '/');
        
        $response = $this->makeRequest('MKCOL', $url);
        return $response !== false;
    }
    
    /**
     * Delete a file or directory
     */
    public function delete(string $path, string $username = null): bool
    {
        $username = $username ?: $this->config['auth']['username'];
        $url = $this->webdavUrl . $username . '/' . ltrim($path, '/');
        
        $response = $this->makeRequest('DELETE', $url);
        return $response !== false;
    }
    
    /**
     * Move/rename a file or directory
     */
    public function move(string $fromPath, string $toPath, string $username = null): bool
    {
        $username = $username ?: $this->config['auth']['username'];
        $fromUrl = $this->webdavUrl . $username . '/' . ltrim($fromPath, '/');
        $toUrl = $this->webdavUrl . $username . '/' . ltrim($toPath, '/');

        $this->log('debug', "Moving file from $fromUrl to $toUrl");

        $response = $this->makeRequest('MOVE', $fromUrl, [
            'Destination: ' . $toUrl
        ]);

        if ($response !== false) {
            $this->log('debug', "File moved successfully");
            return true;
        } else {
            $this->log('error', "Failed to move file from $fromPath to $toPath");
            return false;
        }
    }
    
    /**
     * Copy a file or directory
     */
    public function copy(string $fromPath, string $toPath, string $username = null): bool
    {
        $username = $username ?: $this->config['auth']['username'];
        $fromUrl = $this->webdavUrl . $username . '/' . ltrim($fromPath, '/');
        $toUrl = $this->webdavUrl . $username . '/' . ltrim($toPath, '/');
        
        $response = $this->makeRequest('COPY', $fromUrl, [
            'Destination: ' . $toUrl
        ]);
        
        return $response !== false;
    }
    
    /**
     * Get file/folder properties
     */
    public function getProperties(string $path, string $username = null): array|false
    {
        $username = $username ?: $this->config['auth']['username'];
        $url = $this->webdavUrl . $username . '/' . ltrim($path, '/');
        
        $xml = '<?xml version="1.0"?>
        <d:propfind xmlns:d="DAV:" xmlns:oc="http://owncloud.org/ns">
            <d:prop>
                <d:displayname />
                <d:getcontentlength />
                <d:getcontenttype />
                <d:getlastmodified />
                <d:resourcetype />
                <oc:size />
                <oc:permissions />
            </d:prop>
        </d:propfind>';
        
        $response = $this->makeRequest('PROPFIND', $url, [
            'Content-Type: application/xml',
            'Depth: 0'
        ], $xml);
        
        if ($response === false) {
            return false;
        }
        
        $parsed = $this->parseDirectoryListing($response);
        return !empty($parsed) ? $parsed[0] : false;
    }
    
    /**
     * Check if file or directory exists
     */
    public function exists(string $path, string $username = null): bool
    {
        return $this->getProperties($path, $username) !== false;
    }
    
    /**
     * Get direct download URL for a file
     */
    public function getDownloadUrl(string $path, string $username = null): string
    {
        $username = $username ?: $this->config['auth']['username'];
        return $this->webdavUrl . $username . '/' . ltrim($path, '/');
    }

    /**
     * Get debug information about the client configuration
     */
    public function getDebugInfo(): array
    {
        return [
            'base_url' => $this->baseUrl,
            'webdav_url' => $this->webdavUrl,
            'username' => $this->config['auth']['username'] ?? 'not set',
            'has_password' => !empty($this->config['auth']['password']),
            'timeout' => $this->config['server']['timeout'] ?? 'not set',
            'verify_ssl' => $this->config['server']['verify_ssl'] ?? 'not set'
        ];
    }
    
    /**
     * Make HTTP request to Nextcloud
     */
    private function makeRequest(string $method, string $url, array $headers = [], $data = null): string|false
    {
        $ch = curl_init();
        
        $defaultHeaders = array_merge($this->authHeaders, [
            'User-Agent: Strix-ERP/1.0'
        ]);
        
        $allHeaders = array_merge($defaultHeaders, $headers);
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $allHeaders,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->config['server']['timeout'],
            CURLOPT_SSL_VERIFYPEER => $this->config['server']['verify_ssl'],
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3
        ]);
        
        if ($data !== null) {
            if (is_resource($data)) {
                curl_setopt($ch, CURLOPT_INFILE, $data);
                curl_setopt($ch, CURLOPT_UPLOAD, true);
            } else {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            }
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($error) {
            $this->log('error', "cURL error: $error for $method $url");
            return false;
        }

        if ($httpCode >= 400) {
            $this->log('error', "HTTP error $httpCode for $method $url. Response: " . substr($response, 0, 500));
            return false;
        }

        $this->log('debug', "Request successful: $method $url (HTTP $httpCode)");
        return $response;
    }
    
    /**
     * Parse WebDAV directory listing response
     */
    private function parseDirectoryListing(string $xml): array
    {
        $items = [];

        try {
            $this->log('debug', "Parsing XML response, length: " . strlen($xml));

            $doc = new \DOMDocument();
            $doc->loadXML($xml);

            $xpath = new \DOMXPath($doc);
            $xpath->registerNamespace('d', 'DAV:');
            $xpath->registerNamespace('oc', 'http://owncloud.org/ns');
            $xpath->registerNamespace('nc', 'http://nextcloud.org/ns');

            $responses = $xpath->query('//d:response');
            $this->log('debug', "Found " . $responses->length . " responses in XML");

            foreach ($responses as $response) {
                $href = $xpath->query('d:href', $response)->item(0)?->textContent;

                if (!$href) {
                    $this->log('debug', "Skipping response without href");
                    continue;
                }

                // Clean up the path first to check if it's the parent directory
                $cleanPath = urldecode($href);
                $webdavPrefix = '/remote.php/dav/files/' . $this->config['auth']['username'];
                if (str_starts_with($cleanPath, $webdavPrefix)) {
                    $cleanPath = substr($cleanPath, strlen($webdavPrefix));
                }
                if (!str_starts_with($cleanPath, '/')) {
                    $cleanPath = '/' . $cleanPath;
                }

                // Skip only the exact parent directory we're listing, not all directories
                if ($cleanPath === '/' || $cleanPath === '') {
                    $this->log('debug', "Skipping parent directory entry: $href");
                    continue;
                }

                $props = $xpath->query('d:propstat/d:prop', $response)->item(0);
                if (!$props) {
                    $this->log('debug', "Skipping response without props for: $href");
                    continue;
                }

                // Determine if this is a directory
                $isDirectory = $xpath->query('d:resourcetype/d:collection', $props)->length > 0;

                // Fallback: if the original href ends with '/', it's likely a directory
                if (!$isDirectory && str_ends_with($href, '/')) {
                    $isDirectory = true;
                }

                // Clean up the path for directories (remove trailing slash)
                $finalPath = $cleanPath;
                if ($isDirectory && str_ends_with($finalPath, '/') && $finalPath !== '/') {
                    $finalPath = rtrim($finalPath, '/');
                }

                $item = [
                    'path' => $finalPath,
                    'name' => $xpath->query('d:displayname', $props)->item(0)?->textContent ?: basename($finalPath),
                    'size' => (int) ($xpath->query('oc:size', $props)->item(0)?->textContent ?: 0),
                    'content_type' => $xpath->query('d:getcontenttype', $props)->item(0)?->textContent ?: '',
                    'last_modified' => $xpath->query('d:getlastmodified', $props)->item(0)?->textContent ?: '',
                    'permissions' => $xpath->query('oc:permissions', $props)->item(0)?->textContent ?: '',
                    'is_directory' => $isDirectory
                ];

                $this->log('debug', "Parsed item: " . $item['name'] . " (" . $item['path'] . ") - " . ($item['is_directory'] ? 'DIR' : 'FILE'));
                $items[] = $item;
            }
        } catch (Exception $e) {
            $this->log('error', "Failed to parse directory listing: " . $e->getMessage());
            $this->log('debug', "XML content: " . substr($xml, 0, 1000));
        }

        $this->log('debug', "Parsed " . count($items) . " items total");
        return $items;
    }
    
    /**
     * Log messages
     */
    private function log(string $level, string $message): void
    {
        if (!$this->config['logging']['enabled']) {
            return;
        }

        $logLevels = ['debug' => 0, 'info' => 1, 'warning' => 2, 'error' => 3];
        $currentLevel = $logLevels[$this->config['logging']['level']] ?? 1;
        $messageLevel = $logLevels[$level] ?? 1;

        if ($messageLevel >= $currentLevel) {
            $timestamp = date('Y-m-d H:i:s');
            $logMessage = "[$timestamp] [Nextcloud] [$level] $message";

            // Log to error log
            error_log($logMessage);

            // Also log to a specific file for easier debugging
            $logFile = __DIR__ . '/../../logs/nextcloud.log';
            $logDir = dirname($logFile);
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }
            file_put_contents($logFile, $logMessage . PHP_EOL, FILE_APPEND | LOCK_EX);
        }
    }
}
