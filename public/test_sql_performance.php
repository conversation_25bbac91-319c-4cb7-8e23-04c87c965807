<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Core\Database;
use Strix\ERP\Models\User;
use Strix\ERP\Models\Group;
use Strix\ERP\Models\Task;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

// Mock login
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['user'] = [
    'id' => 1,
    'username' => 'admin',
    'role' => 'admin',
    'permissions' => ['users.view', 'groups.view', 'tasks.view']
];

echo "<!DOCTYPE html><html><head><title>SQL Performance Test</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { border: 1px solid #ddd; margin: 15px 0; padding: 20px; border-radius: 8px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .performance { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; }
    .fast { border-left: 4px solid #28a745; }
    .medium { border-left: 4px solid #ffc107; }
    .slow { border-left: 4px solid #dc3545; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background: #f8f9fa; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style></head><body>";

echo "<h1>🚀 SQL Performance Test</h1>";

function measureQuery($description, $callback) {
    echo "<div class='test-section'>";
    echo "<h3>$description</h3>";
    
    $start = microtime(true);
    $memoryBefore = memory_get_usage();
    
    try {
        $result = $callback();
        $end = microtime(true);
        $memoryAfter = memory_get_usage();
        
        $time = round(($end - $start) * 1000, 2); // Convert to milliseconds
        $memory = round(($memoryAfter - $memoryBefore) / 1024, 2); // Convert to KB
        
        $performanceClass = 'fast';
        if ($time > 100) $performanceClass = 'medium';
        if ($time > 500) $performanceClass = 'slow';
        
        echo "<div class='performance $performanceClass'>";
        echo "<strong>⏱️ Time:</strong> {$time}ms<br>";
        echo "<strong>💾 Memory:</strong> {$memory}KB<br>";
        echo "<strong>📊 Records:</strong> " . (is_array($result) ? count($result) : 'N/A');
        echo "</div>";
        
        if ($time > 100) {
            echo "<p class='warning'>⚠️ Query is slower than 100ms - consider optimization</p>";
        } else {
            echo "<p class='success'>✅ Query performance is good</p>";
        }
        
        return $result;
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        return null;
    }
    
    echo "</div>";
}

// Test 1: Old vs New User pagination
echo "<h2>👥 User Pagination Tests</h2>";

measureQuery("❌ OLD: User::all() + array_slice (inefficient)", function() {
    $users = User::all();
    return array_slice($users, 0, 20);
});

measureQuery("✅ NEW: Optimized pagination query", function() {
    return Database::fetchAll(
        "SELECT * FROM users ORDER BY created_at DESC LIMIT 20 OFFSET 0"
    );
});

measureQuery("✅ NEW: Model::paginate() method", function() {
    return User::paginate(1, 20, [], 'created_at DESC');
});

// Test 2: Search performance
echo "<h2>🔍 Search Performance Tests</h2>";

measureQuery("❌ OLD: Load all + PHP filter", function() {
    $users = User::all();
    return array_filter($users, function($user) {
        return strpos(strtolower($user->first_name), 'a') !== false;
    });
});

measureQuery("✅ NEW: SQL LIKE search", function() {
    return Database::fetchAll(
        "SELECT * FROM users WHERE first_name LIKE ? LIMIT 20",
        ['%a%']
    );
});

// Test 3: Group operations
echo "<h2>👥 Group Operations Tests</h2>";

measureQuery("❌ OLD: Multiple individual group operations", function() {
    // Simulate old way of updating user groups
    $user = User::find(1);
    if ($user) {
        $groups = $user->getGroups(); // 1 query
        // Would be N queries to remove each group
        // Would be M queries to add new groups
        return count($groups);
    }
    return 0;
});

measureQuery("✅ NEW: Batch group operations", function() {
    // Simulate new optimized way
    $userId = 1;
    
    // Single DELETE query
    Database::query("DELETE FROM user_groups WHERE user_id = ?", [$userId]);
    
    // Single batch INSERT (simulated)
    $groupIds = [1, 2, 3];
    if (!empty($groupIds)) {
        $values = [];
        $placeholders = [];
        foreach ($groupIds as $groupId) {
            $values[] = $userId;
            $values[] = $groupId;
            $values[] = 1; // assigned_by
            $values[] = date('Y-m-d H:i:s');
            $placeholders[] = "(?, ?, ?, ?)";
        }
        
        Database::query(
            "INSERT INTO user_groups (user_id, group_id, assigned_by, assigned_at) VALUES " . 
            implode(", ", $placeholders),
            $values
        );
    }
    
    return count($groupIds);
});

// Test 4: Task queries (already optimized)
echo "<h2>📋 Task Query Performance</h2>";

measureQuery("✅ GOOD: Optimized task query with JOINs", function() {
    return Database::fetchAll(
        "SELECT t.*, 
                tt.name as type_name, tt.color as type_color,
                ts.name as status_name, ts.color as status_color,
                u1.first_name as creator_first_name, u1.last_name as creator_last_name
         FROM tasks t
         INNER JOIN task_types tt ON t.task_type_id = tt.id
         INNER JOIN task_statuses ts ON t.status_id = ts.id
         INNER JOIN users u1 ON t.created_by = u1.id
         ORDER BY t.created_at DESC
         LIMIT 20"
    );
});

// Test 5: Count queries
echo "<h2>🔢 Count Query Performance</h2>";

measureQuery("❌ OLD: count(Model::all())", function() {
    return count(User::all());
});

measureQuery("✅ NEW: SQL COUNT(*)", function() {
    return (int) Database::fetchColumn("SELECT COUNT(*) FROM users");
});

// Test 6: Index usage analysis
echo "<h2>📊 Index Usage Analysis</h2>";

echo "<div class='test-section'>";
echo "<h3>Current Database Indexes</h3>";

try {
    $indexes = Database::fetchAll("SHOW INDEX FROM users");
    
    echo "<table>";
    echo "<tr><th>Table</th><th>Key Name</th><th>Column</th><th>Unique</th></tr>";
    
    foreach ($indexes as $index) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($index['Table']) . "</td>";
        echo "<td>" . htmlspecialchars($index['Key_name']) . "</td>";
        echo "<td>" . htmlspecialchars($index['Column_name']) . "</td>";
        echo "<td>" . ($index['Non_unique'] ? 'No' : 'Yes') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Could not fetch index information: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Test 7: Query execution plans
echo "<h2>🔍 Query Execution Plans</h2>";

$testQueries = [
    "User search" => "SELECT * FROM users WHERE first_name LIKE '%a%' LIMIT 10",
    "User pagination" => "SELECT * FROM users ORDER BY created_at DESC LIMIT 20 OFFSET 0",
    "Task with JOINs" => "SELECT t.*, tt.name FROM tasks t INNER JOIN task_types tt ON t.task_type_id = tt.id LIMIT 10"
];

foreach ($testQueries as $description => $query) {
    echo "<div class='test-section'>";
    echo "<h3>$description</h3>";
    echo "<pre>" . htmlspecialchars($query) . "</pre>";
    
    try {
        $explain = Database::fetchAll("EXPLAIN $query");
        
        echo "<table>";
        echo "<tr><th>Type</th><th>Key</th><th>Rows</th><th>Extra</th></tr>";
        
        foreach ($explain as $row) {
            $rowClass = '';
            if (isset($row['rows']) && $row['rows'] > 1000) $rowClass = 'warning';
            if (isset($row['type']) && $row['type'] === 'ALL') $rowClass = 'error';
            
            echo "<tr class='$rowClass'>";
            echo "<td>" . htmlspecialchars($row['type'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($row['key'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($row['rows'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($row['Extra'] ?? '') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Could not explain query: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "</div>";
}

// Summary and recommendations
echo "<div class='test-section'>";
echo "<h2>📋 Summary and Recommendations</h2>";

echo "<h3>✅ Optimizations Applied:</h3>";
echo "<ul>";
echo "<li>UserController pagination now uses SQL LIMIT/OFFSET instead of PHP array_slice</li>";
echo "<li>GroupController pagination optimized similarly</li>";
echo "<li>User group updates use batch operations instead of individual queries</li>";
echo "<li>Added Model::paginate() and Model::count() methods for efficient pagination</li>";
echo "</ul>";

echo "<h3>🚀 Next Steps:</h3>";
echo "<ul>";
echo "<li>Run the index migration: <code>database/migrations/add_performance_indexes.sql</code></li>";
echo "<li>Monitor slow query log for queries taking > 100ms</li>";
echo "<li>Consider adding query caching for frequently accessed data</li>";
echo "<li>Implement database connection pooling for high traffic</li>";
echo "</ul>";

echo "<h3>📊 Performance Targets:</h3>";
echo "<ul>";
echo "<li><span class='success'>Good:</span> < 100ms query time</li>";
echo "<li><span class='warning'>Acceptable:</span> 100-500ms query time</li>";
echo "<li><span class='error'>Needs optimization:</span> > 500ms query time</li>";
echo "</ul>";

echo "</div>";

echo "<div class='test-section'>";
echo "<h2>🔗 Quick Actions</h2>";
echo "<a href='/admin/users' style='padding: 10px 15px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 3px;'>👥 Test Users Page</a>";
echo "<a href='/admin/groups' style='padding: 10px 15px; margin: 5px; background: #28a745; color: white; text-decoration: none; border-radius: 3px;'>👥 Test Groups Page</a>";
echo "<a href='/admin/tasks' style='padding: 10px 15px; margin: 5px; background: #ffc107; color: black; text-decoration: none; border-radius: 3px;'>📋 Test Tasks Page</a>";
echo "</div>";

echo "</body></html>";
