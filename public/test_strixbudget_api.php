<?php
/**
 * Test script for StrixBudget API connection
 * This script helps test the real API connection without mock mode
 */

require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$envFile = __DIR__ . '/../.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        [$name, $value] = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

use Strix\ERP\Services\StrixBudgetClient;

?>
<!DOCTYPE html>
<html lang="bg">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StrixBudget API Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1>🔧 StrixBudget API Test</h1>
        <p class="text-muted">Тестване на връзката към StrixBudget API сървър</p>

        <?php
        echo "<h2>1. Environment Configuration</h2>";
        $envVars = [
            'STRIXBUDGET_API_URL',
            'STRIXBUDGET_MOCK_MODE',
            'STRIXBUDGET_TIMEOUT',
            'STRIXBUDGET_VERIFY_SSL'
        ];

        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>Variable</th><th>Value</th><th>Status</th></tr></thead><tbody>";
        foreach ($envVars as $var) {
            $value = $_ENV[$var] ?? 'not set';
            $status = $value !== 'not set' ? '✅ Set' : '❌ Not set';
            $class = $value !== 'not set' ? 'success' : 'error';
            echo "<tr><td><code>$var</code></td><td><code>$value</code></td><td class='$class'>$status</td></tr>";
        }
        echo "</tbody></table>";

        echo "<h2>2. Client Initialization</h2>";
        try {
            $apiUrl = $_ENV['STRIXBUDGET_API_URL'] ?? 'http://localhost:8000/api';
            $client = new StrixBudgetClient($apiUrl);
            echo "<p class='success'>✅ StrixBudgetClient created successfully</p>";
            echo "<p><strong>API URL:</strong> <code>$apiUrl</code></p>";
            
            $debugInfo = $client->getDebugInfo();
            echo "<h3>Client Debug Info:</h3>";
            echo "<pre>" . json_encode($debugInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Error creating client: " . $e->getMessage() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }

        echo "<h2>3. API Availability Test</h2>";
        if (isset($client)) {
            try {
                // Force disable mock mode for real test
                $client->setMockMode(false);
                echo "<p class='info'>🔧 Mock mode disabled for testing</p>";
                
                $testResult = $client->testConnection();
                
                if ($testResult['success']) {
                    echo "<p class='success'>✅ " . $testResult['message'] . "</p>";
                    if (isset($testResult['user'])) {
                        echo "<h4>User Info:</h4>";
                        echo "<pre>" . json_encode($testResult['user'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                    }
                } else {
                    echo "<p class='error'>❌ " . $testResult['message'] . "</p>";
                    if (isset($testResult['debug_info'])) {
                        echo "<h4>Debug Info:</h4>";
                        echo "<pre>" . json_encode($testResult['debug_info'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                    }
                    if (isset($testResult['error_details'])) {
                        echo "<h4>Error Details:</h4>";
                        echo "<pre>" . json_encode($testResult['error_details'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                    }
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>❌ Exception during test: " . $e->getMessage() . "</p>";
                echo "<pre>" . $e->getTraceAsString() . "</pre>";
            }
        }

        echo "<h2>4. Manual API Test</h2>";
        if (isset($client)) {
            echo "<form method='post' class='mt-3'>";
            echo "<div class='row'>";
            echo "<div class='col-md-6'>";
            echo "<label class='form-label'>API URL:</label>";
            echo "<input type='text' name='test_url' class='form-control' value='" . htmlspecialchars($apiUrl) . "'>";
            echo "</div>";
            echo "<div class='col-md-6'>";
            echo "<label class='form-label'>Endpoint:</label>";
            echo "<input type='text' name='test_endpoint' class='form-control' value='/health' placeholder='/health'>";
            echo "</div>";
            echo "</div>";
            echo "<div class='mt-3'>";
            echo "<button type='submit' name='manual_test' class='btn btn-primary'>🔍 Test Manual Request</button>";
            echo "</div>";
            echo "</form>";

            if (isset($_POST['manual_test'])) {
                echo "<h3>Manual Test Results:</h3>";
                $testUrl = $_POST['test_url'] ?? $apiUrl;
                $testEndpoint = $_POST['test_endpoint'] ?? '/health';
                
                try {
                    $testClient = new StrixBudgetClient($testUrl);
                    $testClient->setMockMode(false);
                    
                    // Try to make a simple request
                    $ch = curl_init();
                    curl_setopt_array($ch, [
                        CURLOPT_URL => $testUrl . $testEndpoint,
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_TIMEOUT => 10,
                        CURLOPT_CONNECTTIMEOUT => 5,
                        CURLOPT_SSL_VERIFYPEER => false,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_MAXREDIRS => 3,
                        CURLOPT_USERAGENT => 'Strix-ERP/1.0 Test'
                    ]);

                    $response = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    $error = curl_error($ch);
                    $info = curl_getinfo($ch);
                    curl_close($ch);

                    echo "<p><strong>Request URL:</strong> <code>" . htmlspecialchars($testUrl . $testEndpoint) . "</code></p>";
                    echo "<p><strong>HTTP Code:</strong> <span class='" . ($httpCode >= 200 && $httpCode < 400 ? 'success' : 'error') . "'>$httpCode</span></p>";
                    
                    if ($error) {
                        echo "<p class='error'><strong>cURL Error:</strong> $error</p>";
                    }
                    
                    if ($response) {
                        echo "<h4>Response:</h4>";
                        echo "<pre>" . htmlspecialchars(substr($response, 0, 1000)) . "</pre>";
                        if (strlen($response) > 1000) {
                            echo "<p class='text-muted'>Response truncated to 1000 characters</p>";
                        }
                    }
                    
                    echo "<h4>Connection Info:</h4>";
                    echo "<pre>" . json_encode($info, JSON_PRETTY_PRINT) . "</pre>";
                    
                } catch (Exception $e) {
                    echo "<p class='error'>❌ Manual test error: " . $e->getMessage() . "</p>";
                }
            }
        }
        ?>

        <div class="mt-5">
            <h2>5. Next Steps</h2>
            <div class="alert alert-info">
                <h5>За да използвате истински StrixBudget API:</h5>
                <ol>
                    <li>Уверете се, че StrixBudget API сървърът работи на посочения URL</li>
                    <li>Проверете че <code>STRIXBUDGET_MOCK_MODE=false</code> в .env файла</li>
                    <li>Настройте правилния <code>STRIXBUDGET_API_URL</code></li>
                    <li>Конфигурирайте API credentials в админ панела</li>
                    <li>Тествайте връзката чрез админ панела</li>
                </ol>
            </div>
        </div>

        <div class="mt-3">
            <a href="/admin/strixbudget/settings" class="btn btn-success">🔧 Отиди към настройки</a>
            <a href="/admin/strixbudget" class="btn btn-primary">📊 StrixBudget Dashboard</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
