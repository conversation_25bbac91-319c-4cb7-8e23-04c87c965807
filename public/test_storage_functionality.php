<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Storage Functionality</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .feature-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; }
        .feature-card h3 { margin-top: 0; color: #333; }
        .demo-actions { margin-top: 10px; }
        .demo-actions button { margin: 5px; padding: 8px 15px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
    </style>
</head>
<body>
    <h1>🗄️ Storage/Nextcloud Integration Test</h1>
    
    <div class="test-section">
        <h2>📊 System Status</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <h3>🔐 Permissions</h3>
                <ul>
                    <li>storage.view: <?= $app->hasPermission('storage.view') ? '✅' : '❌' ?></li>
                    <li>storage.upload: <?= $app->hasPermission('storage.upload') ? '✅' : '❌' ?></li>
                    <li>storage.download: <?= $app->hasPermission('storage.download') ? '✅' : '❌' ?></li>
                    <li>storage.create: <?= $app->hasPermission('storage.create') ? '✅' : '❌' ?></li>
                    <li>storage.edit: <?= $app->hasPermission('storage.edit') ? '✅' : '❌' ?></li>
                    <li>storage.delete: <?= $app->hasPermission('storage.delete') ? '✅' : '❌' ?></li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>⚙️ Configuration</h3>
                <ul>
                    <li>Nextcloud URL: <?= !empty($_ENV['NEXTCLOUD_URL']) ? '✅ Configured' : '❌ Not set' ?></li>
                    <li>Username: <?= !empty($_ENV['NEXTCLOUD_USERNAME']) ? '✅ Set' : '❌ Not set' ?></li>
                    <li>Password: <?= !empty($_ENV['NEXTCLOUD_PASSWORD']) ? '✅ Set' : '❌ Not set' ?></li>
                    <li>Mode: <?= (!empty($_ENV['NEXTCLOUD_URL']) && !empty($_ENV['NEXTCLOUD_USERNAME'])) ? '🌐 Real Nextcloud' : '🎭 Demo Mode' ?></li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🧪 Functionality Tests</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <h3>📁 File Browser</h3>
                <p>Browse files and folders in your Nextcloud storage</p>
                <div class="demo-actions">
                    <button class="btn-primary" onclick="window.open('/admin/storage', '_blank')">
                        Open File Browser
                    </button>
                </div>
                <p class="info">✨ Features: List/Grid view, Breadcrumb navigation, File icons</p>
            </div>
            
            <div class="feature-card">
                <h3>📤 File Upload</h3>
                <p>Upload files directly to Nextcloud through the browser</p>
                <div class="demo-actions">
                    <button class="btn-success" onclick="testUpload()">
                        Test Upload
                    </button>
                </div>
                <div id="uploadResult"></div>
                <p class="info">✨ Features: Multiple file upload, Progress tracking, File validation</p>
            </div>
            
            <div class="feature-card">
                <h3>📁 Folder Management</h3>
                <p>Create new folders in your Nextcloud storage</p>
                <div class="demo-actions">
                    <button class="btn-info" onclick="testCreateFolder()">
                        Test Create Folder
                    </button>
                </div>
                <div id="folderResult"></div>
                <p class="info">✨ Features: Folder creation, Name validation, Instant feedback</p>
            </div>
            
            <div class="feature-card">
                <h3>💾 Download Files</h3>
                <p>Download files from Nextcloud to your computer</p>
                <div class="demo-actions">
                    <button class="btn-warning" onclick="window.open('/admin/storage/download?path=/README.md', '_blank')">
                        Test Download
                    </button>
                </div>
                <p class="info">✨ Features: Direct download, Proper MIME types, Secure access</p>
            </div>
            
            <div class="feature-card">
                <h3>✏️ Rename Items</h3>
                <p>Rename files and folders</p>
                <div class="demo-actions">
                    <button class="btn-warning" onclick="testRename()">
                        Test Rename
                    </button>
                </div>
                <div id="renameResult"></div>
                <p class="info">✨ Features: In-place editing, Name validation, Conflict detection</p>
            </div>
            
            <div class="feature-card">
                <h3>🗑️ Delete Items</h3>
                <p>Delete files and folders from Nextcloud</p>
                <div class="demo-actions">
                    <button class="btn-danger" onclick="testDelete()">
                        Test Delete
                    </button>
                </div>
                <div id="deleteResult"></div>
                <p class="info">✨ Features: Confirmation dialogs, Safe deletion, Instant feedback</p>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🎯 Integration Features</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <h3>🔗 ERP Integration</h3>
                <ul>
                    <li>✅ Integrated with ERP navigation menu</li>
                    <li>✅ Uses ERP authentication and permissions</li>
                    <li>✅ Consistent UI with rest of ERP system</li>
                    <li>✅ CSRF protection for all operations</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>🛡️ Security Features</h3>
                <ul>
                    <li>✅ Permission-based access control</li>
                    <li>✅ File type validation</li>
                    <li>✅ File size limits</li>
                    <li>✅ Secure file operations</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>🎨 User Experience</h3>
                <ul>
                    <li>✅ Responsive design</li>
                    <li>✅ Drag & drop upload (future)</li>
                    <li>✅ Real-time feedback</li>
                    <li>✅ Error handling</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>⚡ Performance</h3>
                <ul>
                    <li>✅ Efficient WebDAV communication</li>
                    <li>✅ Chunked file uploads (future)</li>
                    <li>✅ Caching support</li>
                    <li>✅ Optimized file operations</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📋 Next Steps</h2>
        <div class="feature-card">
            <h3>🚀 To enable full Nextcloud integration:</h3>
            <ol>
                <li><strong>Configure Nextcloud connection:</strong>
                    <ul>
                        <li>Edit <code>.env</code> file</li>
                        <li>Set <code>NEXTCLOUD_URL</code> to your Nextcloud server</li>
                        <li>Set <code>NEXTCLOUD_USERNAME</code> and <code>NEXTCLOUD_PASSWORD</code></li>
                    </ul>
                </li>
                <li><strong>Create App Password in Nextcloud:</strong>
                    <ul>
                        <li>Go to Nextcloud Settings → Security</li>
                        <li>Create new App Password for ERP system</li>
                        <li>Use this password in <code>NEXTCLOUD_PASSWORD</code></li>
                    </ul>
                </li>
                <li><strong>Test the connection:</strong>
                    <ul>
                        <li><a href="/test_nextcloud_config.php" target="_blank">Run Nextcloud Configuration Test</a></li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>
    
    <div class="demo-actions" style="text-align: center; margin-top: 30px;">
        <button class="btn-primary" onclick="window.open('/admin/storage', '_blank')">
            🗄️ Open Storage Manager
        </button>
        <button class="btn-info" onclick="window.open('/test_nextcloud_config.php', '_blank')">
            ⚙️ Configuration Test
        </button>
        <button class="btn-success" onclick="window.location.href='/admin'">
            🏠 Back to Dashboard
        </button>
    </div>
    
    <script>
    function testUpload() {
        const result = document.getElementById('uploadResult');
        result.innerHTML = '<p class="info">📤 Upload functionality is available in the Storage Manager. Click "Open File Browser" and use the "Качи файлове" button.</p>';
    }
    
    function testCreateFolder() {
        const result = document.getElementById('folderResult');
        result.innerHTML = '<p class="info">📁 Folder creation is available in the Storage Manager. Click "Open File Browser" and use the "Нова папка" button.</p>';
    }
    
    function testRename() {
        const result = document.getElementById('renameResult');
        result.innerHTML = '<p class="info">✏️ Rename functionality is available in the Storage Manager. Click the edit (✏️) button next to any file or folder.</p>';
    }
    
    function testDelete() {
        const result = document.getElementById('deleteResult');
        result.innerHTML = '<p class="info">🗑️ Delete functionality is available in the Storage Manager. Click the delete (🗑️) button next to any file or folder.</p>';
    }
    </script>
</body>
</html>
