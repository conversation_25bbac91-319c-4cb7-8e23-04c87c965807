RewriteEngine On

# Handle Angular and other front-end router, API requests
RewriteCond %{REQUEST_URI} ^/(api|assets)/
RewriteRule ^(.*)$ index.php [QSA,L]

# Handle requests that don't point to a file
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Prevent access to sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.json">
    Order allow,deny
    <PERSON>y from all
</Files>

<Files "composer.lock">
    Order allow,deny
    Deny from all
</Files>
