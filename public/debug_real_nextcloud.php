<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Services\NextcloudClient;
use Strix\ERP\Models\UserNextcloudSettings;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

// Mock login
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['user'] = [
    'id' => 1,
    'username' => 'admin',
    'role' => 'admin',
    'permissions' => ['storage.view', 'storage.download', 'storage.edit']
];

echo "<!DOCTYPE html><html><head><title>Debug Real Nextcloud</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    .warning { color: orange; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    .debug-info { background: #e8f4fd; padding: 10px; border-radius: 3px; margin: 10px 0; }
</style></head><body>";

echo "<h1>🔧 Debug Real Nextcloud Client</h1>";

// Test 1: Check configuration
echo "<div class='test'>";
echo "<h2>Test 1: Configuration Check</h2>";

$config = require __DIR__ . '/../config/nextcloud.php';
echo "<div class='debug-info'>";
echo "<h3>Global Configuration:</h3>";
echo "<p><strong>Server URL:</strong> " . htmlspecialchars($config['server']['url']) . "</p>";
echo "<p><strong>WebDAV Path:</strong> " . htmlspecialchars($config['server']['webdav_path']) . "</p>";
echo "<p><strong>Username:</strong> " . htmlspecialchars($config['auth']['username']) . "</p>";
echo "<p><strong>Has Password:</strong> " . (!empty($config['auth']['password']) ? 'YES' : 'NO') . "</p>";
echo "<p><strong>Timeout:</strong> " . $config['server']['timeout'] . "s</p>";
echo "<p><strong>Verify SSL:</strong> " . ($config['server']['verify_ssl'] ? 'YES' : 'NO') . "</p>";
echo "</div>";

// Check if configuration looks valid
$isValidConfig = true;
$configIssues = [];

if (in_array($config['server']['url'], ['https://your-nextcloud-server.com', ''])) {
    $isValidConfig = false;
    $configIssues[] = "Server URL is placeholder or empty";
}

if (in_array($config['auth']['username'], ['your-username', ''])) {
    $isValidConfig = false;
    $configIssues[] = "Username is placeholder or empty";
}

if (in_array($config['auth']['password'], ['your-app-password', 'your-password', ''])) {
    $isValidConfig = false;
    $configIssues[] = "Password is placeholder or empty";
}

if ($isValidConfig) {
    echo "<p class='success'>✅ Configuration looks valid</p>";
} else {
    echo "<p class='error'>❌ Configuration issues found:</p>";
    echo "<ul>";
    foreach ($configIssues as $issue) {
        echo "<li class='error'>" . htmlspecialchars($issue) . "</li>";
    }
    echo "</ul>";
}

echo "</div>";

// Test 2: Try to create NextcloudClient
echo "<div class='test'>";
echo "<h2>Test 2: Create NextcloudClient</h2>";

try {
    $client = new NextcloudClient();
    echo "<p class='success'>✅ NextcloudClient created successfully</p>";
    
    $debugInfo = $client->getDebugInfo();
    echo "<div class='debug-info'>";
    echo "<h3>Client Debug Info:</h3>";
    foreach ($debugInfo as $key => $value) {
        echo "<p><strong>" . ucfirst(str_replace('_', ' ', $key)) . ":</strong> " . htmlspecialchars($value) . "</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Failed to create NextcloudClient: " . htmlspecialchars($e->getMessage()) . "</p>";
    $client = null;
}

echo "</div>";

if ($client) {
    // Test 3: Test connection
    echo "<div class='test'>";
    echo "<h2>Test 3: Test Connection</h2>";
    
    try {
        echo "<p class='info'>Testing connection by listing root directory...</p>";
        $files = $client->listDirectory('/');
        
        echo "<p class='success'>✅ Connection successful! Found " . count($files) . " items</p>";
        
        if (!empty($files)) {
            echo "<h3>Root Directory Contents:</h3>";
            echo "<ul>";
            foreach (array_slice($files, 0, 10) as $file) { // Show first 10 items
                $icon = $file['is_directory'] ? '📁' : '📄';
                echo "<li>$icon " . htmlspecialchars($file['name']) . " (" . htmlspecialchars($file['path']) . ")</li>";
            }
            if (count($files) > 10) {
                echo "<li>... and " . (count($files) - 10) . " more items</li>";
            }
            echo "</ul>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "</div>";
    
    // Test 4: Test specific file operations
    echo "<div class='test'>";
    echo "<h2>Test 4: Test File Operations</h2>";
    
    // Try to find a file to test with
    $testFile = null;
    try {
        $files = $client->listDirectory('/');
        foreach ($files as $file) {
            if (!$file['is_directory'] && $file['size'] > 0 && $file['size'] < 1024*1024) { // Find a small file
                $testFile = $file['path'];
                break;
            }
        }
        
        if ($testFile) {
            echo "<p class='info'>Testing with file: " . htmlspecialchars($testFile) . "</p>";
            
            // Test exists
            $exists = $client->exists($testFile);
            echo "<p class='" . ($exists ? 'success' : 'error') . "'>";
            echo ($exists ? "✅" : "❌") . " File exists check: " . ($exists ? "YES" : "NO");
            echo "</p>";
            
            if ($exists) {
                // Test download
                try {
                    $content = $client->downloadFile($testFile);
                    if ($content !== false) {
                        echo "<p class='success'>✅ Download successful, size: " . strlen($content) . " bytes</p>";
                        echo "<p><strong>Content preview:</strong> " . htmlspecialchars(substr($content, 0, 100)) . "...</p>";
                    } else {
                        echo "<p class='error'>❌ Download failed</p>";
                    }
                } catch (Exception $e) {
                    echo "<p class='error'>❌ Download error: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            }
            
        } else {
            echo "<p class='warning'>⚠️ No suitable test file found in root directory</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error during file operations test: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "</div>";
}

// Test 5: Check user-specific settings
echo "<div class='test'>";
echo "<h2>Test 5: User-Specific Settings</h2>";

try {
    $userSettings = UserNextcloudSettings::getActiveForUser(1);
    
    if ($userSettings) {
        echo "<p class='success'>✅ User has Nextcloud settings configured</p>";
        echo "<div class='debug-info'>";
        echo "<h3>User Settings:</h3>";
        echo "<p><strong>Server URL:</strong> " . htmlspecialchars($userSettings->server_url) . "</p>";
        echo "<p><strong>Username:</strong> " . htmlspecialchars($userSettings->username) . "</p>";
        echo "<p><strong>Has Password:</strong> " . (!empty($userSettings->password) ? 'YES' : 'NO') . "</p>";
        echo "<p><strong>Is Active:</strong> " . ($userSettings->is_active ? 'YES' : 'NO') . "</p>";
        echo "</div>";
        
        // Try to create client with user settings
        try {
            $userClient = new NextcloudClient($userSettings);
            echo "<p class='success'>✅ User-specific NextcloudClient created successfully</p>";
            
            // Test user client
            $userFiles = $userClient->listDirectory('/');
            echo "<p class='success'>✅ User client connection successful! Found " . count($userFiles) . " items</p>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ User client failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
    } else {
        echo "<p class='info'>ℹ️ No user-specific Nextcloud settings found</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error checking user settings: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

echo "<hr>";
echo "<p><a href='/admin/storage'>← Back to Storage</a></p>";
echo "<p><a href='/comprehensive_storage_test.php'>🧪 Run Comprehensive Test</a></p>";

echo "</body></html>";
