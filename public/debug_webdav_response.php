<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\UserNextcloudSettings;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

// Mock login
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['user'] = [
    'id' => 1,
    'username' => 'admin',
    'role' => 'admin',
    'permissions' => ['storage.view', 'storage.download', 'storage.edit']
];

echo "<!DOCTYPE html><html><head><title>Debug WebDAV Response</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .section { border: 1px solid #ddd; margin: 15px 0; padding: 20px; border-radius: 8px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 400px; }
    .xml-response { font-family: monospace; font-size: 12px; }
    .parsed-item { background: #f8f9fa; padding: 8px; margin: 5px 0; border-radius: 3px; border-left: 4px solid #007bff; }
    .directory { border-left-color: #28a745; }
    .file { border-left-color: #ffc107; }
</style></head><body>";

echo "<h1>🔍 Debug WebDAV Response</h1>";

echo "<div class='section'>";
echo "<h2>Raw WebDAV PROPFIND Response</h2>";

try {
    $userSettings = UserNextcloudSettings::getActiveForUser(1);
    
    if (!$userSettings) {
        echo "<p class='error'>❌ No user settings found</p>";
    } else {
        $decryptedPassword = $userSettings->getDecryptedPassword();
        
        if (!$decryptedPassword) {
            echo "<p class='error'>❌ No decrypted password available</p>";
        } else {
            echo "<p class='info'>Making WebDAV PROPFIND request to root directory...</p>";
            
            $ch = curl_init();
            $url = rtrim($userSettings->server_url, '/') . '/remote.php/dav/files/' . $userSettings->username . '/';
            
            $propfindXml = '<?xml version="1.0"?>
<d:propfind xmlns:d="DAV:" xmlns:oc="http://owncloud.org/ns" xmlns:nc="http://nextcloud.org/ns">
    <d:prop>
        <d:displayname />
        <d:getcontentlength />
        <d:getcontenttype />
        <d:getlastmodified />
        <d:resourcetype />
        <oc:size />
        <oc:permissions />
        <nc:has-preview />
    </d:prop>
</d:propfind>';
            
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_CUSTOMREQUEST => 'PROPFIND',
                CURLOPT_HTTPHEADER => [
                    'Authorization: Basic ' . base64_encode($userSettings->username . ':' . $decryptedPassword),
                    'Content-Type: application/xml',
                    'Depth: 1'
                ],
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_SSL_VERIFYPEER => $userSettings->verify_ssl ?? true,
                CURLOPT_POSTFIELDS => $propfindXml
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                echo "<p class='error'>❌ cURL error: " . htmlspecialchars($error) . "</p>";
            } elseif ($httpCode >= 400) {
                echo "<p class='error'>❌ HTTP error: $httpCode</p>";
                echo "<p><strong>Response:</strong></p>";
                echo "<pre>" . htmlspecialchars($response) . "</pre>";
            } else {
                echo "<p class='success'>✅ WebDAV response received (HTTP $httpCode)</p>";
                echo "<p><strong>Response length:</strong> " . strlen($response) . " characters</p>";
                
                echo "<h3>Full XML Response:</h3>";
                echo "<pre class='xml-response'>" . htmlspecialchars($response) . "</pre>";
                
                // Parse the response manually to debug
                echo "<h3>Manual Parsing Debug:</h3>";
                
                try {
                    $doc = new DOMDocument();
                    $doc->loadXML($response);
                    
                    $xpath = new DOMXPath($doc);
                    $xpath->registerNamespace('d', 'DAV:');
                    $xpath->registerNamespace('oc', 'http://owncloud.org/ns');
                    $xpath->registerNamespace('nc', 'http://nextcloud.org/ns');
                    
                    $responses = $xpath->query('//d:response');
                    echo "<p><strong>Found " . $responses->length . " d:response elements</strong></p>";
                    
                    foreach ($responses as $index => $response) {
                        echo "<div class='parsed-item'>";
                        echo "<h4>Response #" . ($index + 1) . ":</h4>";
                        
                        $href = $xpath->query('d:href', $response)->item(0)?->textContent;
                        echo "<p><strong>href:</strong> " . htmlspecialchars($href ?: 'NULL') . "</p>";
                        
                        $props = $xpath->query('d:propstat/d:prop', $response)->item(0);
                        if ($props) {
                            $displayname = $xpath->query('d:displayname', $props)->item(0)?->textContent;
                            $resourcetype = $xpath->query('d:resourcetype', $props)->item(0);
                            $isCollection = $xpath->query('d:collection', $resourcetype)->length > 0;
                            $size = $xpath->query('oc:size', $props)->item(0)?->textContent;
                            $contentType = $xpath->query('d:getcontenttype', $props)->item(0)?->textContent;
                            
                            echo "<p><strong>displayname:</strong> " . htmlspecialchars($displayname ?: 'NULL') . "</p>";
                            echo "<p><strong>is collection:</strong> " . ($isCollection ? 'YES' : 'NO') . "</p>";
                            echo "<p><strong>size:</strong> " . htmlspecialchars($size ?: 'NULL') . "</p>";
                            echo "<p><strong>content-type:</strong> " . htmlspecialchars($contentType ?: 'NULL') . "</p>";
                            
                            // Check what would happen with our parsing logic
                            $cleanPath = urldecode($href);
                            $webdavPrefix = '/remote.php/dav/files/' . $userSettings->username;
                            if (str_starts_with($cleanPath, $webdavPrefix)) {
                                $cleanPath = substr($cleanPath, strlen($webdavPrefix));
                            }
                            if (!str_starts_with($cleanPath, '/')) {
                                $cleanPath = '/' . $cleanPath;
                            }
                            
                            echo "<p><strong>cleaned path:</strong> " . htmlspecialchars($cleanPath) . "</p>";
                            
                            // Check if it would be skipped
                            $wouldSkip = ($cleanPath === '/' || $cleanPath === '');
                            echo "<p><strong>would skip:</strong> " . ($wouldSkip ? 'YES' : 'NO') . "</p>";
                            
                            if (!$wouldSkip) {
                                $finalPath = $cleanPath;
                                if ($isCollection && str_ends_with($finalPath, '/') && $finalPath !== '/') {
                                    $finalPath = rtrim($finalPath, '/');
                                }
                                echo "<p><strong>final path:</strong> " . htmlspecialchars($finalPath) . "</p>";
                                echo "<p><strong>final name:</strong> " . htmlspecialchars(basename($finalPath)) . "</p>";
                            }
                            
                        } else {
                            echo "<p class='warning'>⚠️ No props found</p>";
                        }
                        
                        echo "</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<p class='error'>❌ XML parsing error: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>Quick Actions</h2>";
echo "<a href='/test_directory_listing.php' style='padding: 10px 15px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 3px;'>🧪 Test Directory Listing</a>";
echo "<a href='/compare_clients.php' style='padding: 10px 15px; margin: 5px; background: #28a745; color: white; text-decoration: none; border-radius: 3px;'>🔄 Compare Clients</a>";
echo "<a href='/view_nextcloud_logs.php' style='padding: 10px 15px; margin: 5px; background: #ffc107; color: black; text-decoration: none; border-radius: 3px;'>📋 View Logs</a>";
echo "</div>";

echo "</body></html>";
