<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\User;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

echo "<h1>Debug Menu Permissions</h1>";

echo "<h2>Session Status:</h2>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Is Logged In:</strong> " . ($app->isLoggedIn() ? 'Yes' : 'No') . "</p>";

if ($app->isLoggedIn()) {
    $currentUser = $app->getCurrentUser();
    echo "<h2>Current User Data:</h2>";
    echo "<pre>" . print_r($currentUser, true) . "</pre>";
    
    echo "<h2>Permission Checks:</h2>";
    $permissions = [
        'tasks.view' => $app->hasPermission('tasks.view'),
        'users.view' => $app->hasPermission('users.view'),
        'groups.view' => $app->hasPermission('groups.view'),
        'admin.dashboard' => $app->hasPermission('admin.dashboard'),
        'reports.view' => $app->hasPermission('reports.view'),
        'admin.system_settings' => $app->hasPermission('admin.system_settings')
    ];
    
    foreach ($permissions as $permission => $hasIt) {
        echo "<p><strong>$permission:</strong> " . ($hasIt ? '✅ Yes' : '❌ No') . "</p>";
    }
    
    echo "<h2>Menu Visibility Logic:</h2>";
    echo "<p>The sidebar checks: \$app->hasPermission('tasks.view')</p>";
    echo "<p>Result: " . ($app->hasPermission('tasks.view') ? '✅ Should show' : '❌ Should hide') . "</p>";
    
    // Check if user permissions array contains tasks.view
    $userPermissions = $currentUser['permissions'] ?? [];
    echo "<h2>User Permissions Array:</h2>";
    echo "<p>Contains 'tasks.view': " . (in_array('tasks.view', $userPermissions) ? '✅ Yes' : '❌ No') . "</p>";
    echo "<p>Total permissions: " . count($userPermissions) . "</p>";
    echo "<details><summary>All permissions</summary><pre>" . print_r($userPermissions, true) . "</pre></details>";
    
} else {
    echo "<p style='color: red;'>User is not logged in!</p>";
    echo "<p><a href='/login'>Go to Login</a></p>";
}

echo "<h2>Actions:</h2>";
echo "<p><a href='/admin'>Go to Admin Dashboard</a></p>";
echo "<p><a href='/refresh_session.php'>Refresh Session</a></p>";
echo "<p><a href='/admin/tasks'>Try to access Tasks directly</a></p>";
