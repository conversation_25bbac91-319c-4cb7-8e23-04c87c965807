<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\UserNextcloudSettings;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

// Mock login
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['user'] = [
    'id' => 1,
    'username' => 'admin',
    'role' => 'admin',
    'permissions' => ['storage.view', 'storage.download', 'storage.edit']
];

echo "<!DOCTYPE html><html><head><title>Debug Password Storage</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .section { border: 1px solid #ddd; margin: 15px 0; padding: 20px; border-radius: 8px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    .form-group { margin: 15px 0; }
    .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
    .form-group input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px; }
    .btn { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
    .btn-primary { background: #007bff; color: white; }
</style></head><body>";

echo "<h1>🔍 Debug Password Storage</h1>";

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_password'])) {
    echo "<div class='section'>";
    echo "<h2>Testing Password Storage</h2>";
    
    $testPassword = $_POST['test_password'];
    echo "<p><strong>Original password:</strong> " . htmlspecialchars($testPassword) . "</p>";
    echo "<p><strong>Password length:</strong> " . strlen($testPassword) . " characters</p>";
    
    try {
        // Test base64 encoding/decoding
        $encoded = base64_encode($testPassword);
        echo "<p><strong>Base64 encoded:</strong> " . htmlspecialchars($encoded) . "</p>";
        
        $decoded = base64_decode($encoded);
        echo "<p><strong>Base64 decoded:</strong> " . htmlspecialchars($decoded) . "</p>";
        
        if ($decoded === $testPassword) {
            echo "<p class='success'>✅ Base64 encoding/decoding works correctly</p>";
        } else {
            echo "<p class='error'>❌ Base64 encoding/decoding failed</p>";
        }
        
        // Test creating settings with this password
        echo "<h3>Testing UserNextcloudSettings Creation:</h3>";
        
        $testData = [
            'server_url' => 'https://cloud.danaildr.eu',
            'username' => 'danaildr',
            'password' => $testPassword,
            'verify_ssl' => true,
            'timeout' => 30,
            'default_folder' => '/ERP_Files',
            'auto_create_folders' => true
        ];
        
        echo "<p class='info'>Creating settings with test data...</p>";
        $settings = UserNextcloudSettings::createOrUpdate(1, $testData);
        
        echo "<p class='success'>✅ Settings created with ID: " . $settings->id . "</p>";
        
        // Check what was actually saved
        echo "<h3>Checking Saved Data:</h3>";
        $savedSettings = UserNextcloudSettings::getActiveForUser(1);
        
        if ($savedSettings) {
            echo "<p><strong>Saved password field:</strong> " . htmlspecialchars($savedSettings->password ?: 'NULL/EMPTY') . "</p>";
            echo "<p><strong>Password field length:</strong> " . strlen($savedSettings->password ?: '') . " characters</p>";
            
            // Test decryption
            $decryptedPassword = $savedSettings->getDecryptedPassword();
            echo "<p><strong>Decrypted password:</strong> " . htmlspecialchars($decryptedPassword ?: 'NULL/FAILED') . "</p>";
            
            if ($decryptedPassword === $testPassword) {
                echo "<p class='success'>✅ Password storage and decryption works correctly!</p>";
            } else {
                echo "<p class='error'>❌ Password storage or decryption failed</p>";
                echo "<p><strong>Expected:</strong> " . htmlspecialchars($testPassword) . "</p>";
                echo "<p><strong>Got:</strong> " . htmlspecialchars($decryptedPassword ?: 'NULL') . "</p>";
            }
            
            // Test connection
            echo "<h3>Testing Connection:</h3>";
            $testResult = $savedSettings->testConnection();
            
            if ($testResult['success']) {
                echo "<p class='success'>✅ Connection test successful!</p>";
                echo "<p><strong>HTTP Code:</strong> " . $testResult['http_code'] . "</p>";
            } else {
                echo "<p class='error'>❌ Connection test failed</p>";
                echo "<p><strong>Error:</strong> " . htmlspecialchars($testResult['error']) . "</p>";
            }
            
        } else {
            echo "<p class='error'>❌ Could not retrieve saved settings</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "</div>";
}

// Show current settings
echo "<div class='section'>";
echo "<h2>Current Settings Analysis</h2>";

try {
    $currentSettings = UserNextcloudSettings::getActiveForUser(1);
    
    if ($currentSettings) {
        echo "<p class='success'>✅ Found current settings</p>";
        echo "<p><strong>ID:</strong> " . $currentSettings->id . "</p>";
        echo "<p><strong>Server URL:</strong> " . htmlspecialchars($currentSettings->server_url) . "</p>";
        echo "<p><strong>Username:</strong> " . htmlspecialchars($currentSettings->username) . "</p>";
        echo "<p><strong>Raw password field:</strong> " . htmlspecialchars($currentSettings->password ?: 'NULL/EMPTY') . "</p>";
        echo "<p><strong>Password field length:</strong> " . strlen($currentSettings->password ?: '') . " characters</p>";
        
        // Test if it looks like base64
        if ($currentSettings->password) {
            $isBase64 = base64_encode(base64_decode($currentSettings->password)) === $currentSettings->password;
            echo "<p><strong>Looks like base64:</strong> " . ($isBase64 ? 'YES' : 'NO') . "</p>";
            
            if ($isBase64) {
                $decoded = base64_decode($currentSettings->password);
                echo "<p><strong>Decoded value:</strong> " . htmlspecialchars($decoded) . "</p>";
                echo "<p><strong>Decoded length:</strong> " . strlen($decoded) . " characters</p>";
            }
        }
        
        // Test decryption method
        $decryptedPassword = $currentSettings->getDecryptedPassword();
        echo "<p><strong>getDecryptedPassword() result:</strong> " . htmlspecialchars($decryptedPassword ?: 'NULL/FAILED') . "</p>";
        
    } else {
        echo "<p class='warning'>⚠️ No current settings found</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Show test form
echo "<div class='section'>";
echo "<h2>Test Password Storage</h2>";
echo "<p>Enter a test password to see how it gets stored and retrieved:</p>";

echo "<form method='POST'>";
echo "<div class='form-group'>";
echo "<label for='test_password'>Test Password:</label>";
echo "<input type='password' id='test_password' name='test_password' placeholder='Enter your Nextcloud app password' required>";
echo "</div>";
echo "<button type='submit' class='btn btn-primary'>Test Password Storage</button>";
echo "</form>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>Quick Links</h2>";
echo "<a href='/check_user_nextcloud_settings.php' class='btn btn-primary'>🔍 Check Settings</a>";
echo "<a href='/create_test_nextcloud_settings.php' class='btn btn-primary'>🔧 Create Settings</a>";
echo "<a href='/admin/user-settings' class='btn btn-primary'>📝 User Settings Page</a>";
echo "</div>";

echo "</body></html>";
