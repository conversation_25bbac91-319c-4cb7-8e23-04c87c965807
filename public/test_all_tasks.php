<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test All Tasks Functionality</title>
    <style>
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>Test All Tasks Functionality</h1>
    
    <div class="test-section">
        <h2>1. Tasks List</h2>
        <p><a href="/admin/tasks" target="_blank">📋 Open Tasks List</a></p>
        <p class="info">Should show all tasks with filters and pagination</p>
    </div>
    
    <div class="test-section">
        <h2>2. Create Task</h2>
        <p><a href="/admin/tasks/create" target="_blank">➕ Open Create Task Form</a></p>
        <p class="info">Should show form with all fields for creating a new task</p>
    </div>
    
    <div class="test-section">
        <h2>3. View Task</h2>
        <p><a href="/admin/tasks/1" target="_blank">👁️ View Task #1</a></p>
        <p class="info">Should show detailed view of task with all information</p>
    </div>
    
    <div class="test-section">
        <h2>4. Edit Task</h2>
        <p><a href="/admin/tasks/1/edit" target="_blank">✏️ Edit Task #1</a></p>
        <p class="info">Should show edit form pre-filled with task data</p>
    </div>
    
    <div class="test-section">
        <h2>5. Delete Task (AJAX)</h2>
        <button onclick="testDelete()">🗑️ Test Delete Task #1</button>
        <div id="deleteResult"></div>
        <p class="info">Should delete task via AJAX and return JSON response</p>
    </div>
    
    <div class="test-section">
        <h2>6. Permissions Test</h2>
        <p>Current user permissions for tasks:</p>
        <ul>
            <li>tasks.view: <?= $app->hasPermission('tasks.view') ? '✅' : '❌' ?></li>
            <li>tasks.create: <?= $app->hasPermission('tasks.create') ? '✅' : '❌' ?></li>
            <li>tasks.edit: <?= $app->hasPermission('tasks.edit') ? '✅' : '❌' ?></li>
            <li>tasks.delete: <?= $app->hasPermission('tasks.delete') ? '✅' : '❌' ?></li>
            <li>tasks.view_all: <?= $app->hasPermission('tasks.view_all') ? '✅' : '❌' ?></li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>7. Navigation Menu</h2>
        <p>Check if "📋 Задачи" appears in the left sidebar menu</p>
        <p><a href="/admin" target="_blank">🏠 Go to Dashboard</a></p>
    </div>
    
    <script>
    function testDelete() {
        const result = document.getElementById('deleteResult');
        result.innerHTML = '<p class="info">Testing delete...</p>';
        
        fetch('/admin/tasks/1', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: '_method=DELETE&_token=<?= $_SESSION['csrf_token'] ?>'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                result.innerHTML = '<p class="success">✅ ' + data.message + '</p>';
            } else {
                result.innerHTML = '<p class="error">❌ ' + (data.error || 'Unknown error') + '</p>';
            }
        })
        .catch(error => {
            result.innerHTML = '<p class="error">❌ Network error: ' + error.message + '</p>';
        });
    }
    </script>
    
    <h2>Summary</h2>
    <p>All task functionality should work properly:</p>
    <ul>
        <li>✅ List tasks with filters</li>
        <li>✅ Create new tasks</li>
        <li>✅ View task details</li>
        <li>✅ Edit existing tasks</li>
        <li>✅ Delete tasks (AJAX)</li>
        <li>✅ Proper permissions checking</li>
        <li>✅ Navigation menu integration</li>
    </ul>
    
    <p><strong>Next steps:</strong></p>
    <ul>
        <li>Test creating a task through the form</li>
        <li>Test editing a task and saving changes</li>
        <li>Test task assignment to users/groups</li>
        <li>Test file attachments (if implemented)</li>
        <li>Test comments (if implemented)</li>
    </ul>
</body>
</html>
