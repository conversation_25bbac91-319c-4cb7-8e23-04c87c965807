<!DOCTYPE html>
<html lang="bg">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StrixBudget API Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .api-response {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-top: 1rem;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1>🚀 StrixBudget API Demo</h1>
        <p class="text-muted">Демонстрация на JavaScript API клиента за StrixBudget</p>

        <!-- Authentication Status -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🔐 Authentication Status</h5>
            </div>
            <div class="card-body">
                <div id="authStatus">
                    <span class="status-indicator status-offline"></span>
                    <span>Не сте влезли</span>
                </div>
                <div id="userInfo" class="mt-2" style="display: none;"></div>
                <button id="logoutBtn" class="btn btn-sm btn-outline-danger mt-2" style="display: none;">Излизане</button>
            </div>
        </div>

        <!-- Login Form -->
        <div class="card mb-4" id="loginCard">
            <div class="card-header">
                <h5>🔑 Login</h5>
            </div>
            <div class="card-body">
                <form id="loginForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Email:</label>
                            <input type="email" id="loginEmail" class="form-control" value="<EMAIL>" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Password:</label>
                            <input type="password" id="loginPassword" class="form-control" value="password" required>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">API URL (optional):</label>
                            <input type="url" id="apiUrl" class="form-control" placeholder="https://api.strixbudget.com">
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <div class="form-check">
                                <input type="checkbox" id="saveCredentials" class="form-check-input">
                                <label class="form-check-label" for="saveCredentials">
                                    Запази credentials
                                </label>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary mt-3">🔐 Влизане</button>
                </form>
            </div>
        </div>

        <!-- API Actions -->
        <div class="card mb-4" id="actionsCard" style="display: none;">
            <div class="card-header">
                <h5>🛠️ API Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-info w-100 mb-2" onclick="getCurrentUser()">👤 Current User</button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-success w-100 mb-2" onclick="getBankAccounts()">🏦 Bank Accounts</button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-warning w-100 mb-2" onclick="getTransactions()">💳 Transactions</button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-primary w-100 mb-2" onclick="getStatistics()">📊 Statistics</button>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-secondary w-100 mb-2" onclick="getCounterparties()">🏢 Counterparties</button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-primary w-100 mb-2" onclick="testConnection()">🔍 Test Connection</button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-success w-100 mb-2" onclick="createSampleTransaction()">➕ Create Transaction</button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-info w-100 mb-2" onclick="refreshData()">🔄 Refresh All</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Response -->
        <div class="card">
            <div class="card-header">
                <h5>📋 API Response</h5>
                <button class="btn btn-sm btn-outline-secondary float-end" onclick="clearResponse()">Clear</button>
            </div>
            <div class="card-body">
                <div id="apiResponse" class="api-response">
                    <em>API отговорите ще се показват тук...</em>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <h3>📖 Usage Examples</h3>
            <pre class="bg-light p-3 rounded"><code>// Initialize API client
const api = new StrixBudgetAPI();

// Login
const result = await api.login('<EMAIL>', 'password');

// Get data
const accounts = await api.getBankAccounts();
const transactions = await api.getTransactions({ limit: 10 });
const stats = await api.getStatistics();

// Create transaction
const newTransaction = await api.createTransaction({
    bank_account_id: 1,
    amount: 100.50,
    description: 'Test transaction',
    type: 'expense'
});</code></pre>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/strixbudget-api.js"></script>
    <script>
        // Initialize API client
        const api = new StrixBudgetAPI();

        // Update UI based on authentication status
        function updateAuthStatus() {
            const isAuth = api.isAuthenticated();
            const authStatus = document.getElementById('authStatus');
            const userInfo = document.getElementById('userInfo');
            const loginCard = document.getElementById('loginCard');
            const actionsCard = document.getElementById('actionsCard');
            const logoutBtn = document.getElementById('logoutBtn');

            if (isAuth) {
                authStatus.innerHTML = '<span class="status-indicator status-online"></span><span>Влезли сте успешно</span>';
                const user = api.getUser();
                if (user) {
                    userInfo.innerHTML = `<strong>Потребител:</strong> ${user.name || user.email || 'Unknown'}`;
                    userInfo.style.display = 'block';
                }
                loginCard.style.display = 'none';
                actionsCard.style.display = 'block';
                logoutBtn.style.display = 'inline-block';
            } else {
                authStatus.innerHTML = '<span class="status-indicator status-offline"></span><span>Не сте влезли</span>';
                userInfo.style.display = 'none';
                loginCard.style.display = 'block';
                actionsCard.style.display = 'none';
                logoutBtn.style.display = 'none';
            }
        }

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            const apiUrl = document.getElementById('apiUrl').value;
            const saveCredentials = document.getElementById('saveCredentials').checked;

            showResponse('🔄 Влизане...', 'info');

            try {
                const result = await api.login(email, password, {
                    apiUrl: apiUrl || undefined,
                    saveCredentials: saveCredentials
                });

                if (result.success) {
                    showResponse(`✅ ${result.message}`, 'success');
                    updateAuthStatus();
                } else {
                    showResponse(`❌ ${result.message}`, 'error');
                }
            } catch (error) {
                showResponse(`❌ Грешка: ${error.message}`, 'error');
            }
        });

        // Logout handler
        document.getElementById('logoutBtn').addEventListener('click', () => {
            api.logout();
            updateAuthStatus();
            showResponse('👋 Излязохте успешно', 'info');
        });

        // API action functions
        async function getCurrentUser() {
            try {
                showResponse('🔄 Зареждане на потребителска информация...', 'info');
                const result = await api.getCurrentUser();
                showResponse(result, result.success ? 'success' : 'error');
            } catch (error) {
                showResponse(`❌ Грешка: ${error.message}`, 'error');
            }
        }

        async function getBankAccounts() {
            try {
                showResponse('🔄 Зареждане на банкови сметки...', 'info');
                const result = await api.getBankAccounts();
                showResponse(result, result.success ? 'success' : 'error');
            } catch (error) {
                showResponse(`❌ Грешка: ${error.message}`, 'error');
            }
        }

        async function getTransactions() {
            try {
                showResponse('🔄 Зареждане на транзакции...', 'info');
                const result = await api.getTransactions({ limit: 10 });
                showResponse(result, result.success ? 'success' : 'error');
            } catch (error) {
                showResponse(`❌ Грешка: ${error.message}`, 'error');
            }
        }

        async function getStatistics() {
            try {
                showResponse('🔄 Зареждане на статистики...', 'info');
                const result = await api.getStatistics();
                showResponse(result, result.success ? 'success' : 'error');
            } catch (error) {
                showResponse(`❌ Грешка: ${error.message}`, 'error');
            }
        }

        async function getCounterparties() {
            try {
                showResponse('🔄 Зареждане на контрагенти...', 'info');
                const result = await api.getCounterparties();
                showResponse(result, result.success ? 'success' : 'error');
            } catch (error) {
                showResponse(`❌ Грешка: ${error.message}`, 'error');
            }
        }

        async function testConnection() {
            try {
                showResponse('🔄 Тестване на връзката...', 'info');
                const result = await api.testConnection();
                showResponse(result, result.success ? 'success' : 'error');
            } catch (error) {
                showResponse(`❌ Грешка: ${error.message}`, 'error');
            }
        }

        async function createSampleTransaction() {
            try {
                showResponse('🔄 Създаване на примерна транзакция...', 'info');
                const result = await api.createTransaction({
                    bank_account_id: 1,
                    amount: Math.round(Math.random() * 1000 * 100) / 100,
                    description: 'Demo transaction ' + new Date().toLocaleTimeString(),
                    type: Math.random() > 0.5 ? 'income' : 'expense'
                });
                showResponse(result, result.success ? 'success' : 'error');
            } catch (error) {
                showResponse(`❌ Грешка: ${error.message}`, 'error');
            }
        }

        async function refreshData() {
            showResponse('🔄 Обновяване на всички данни...', 'info');
            
            try {
                const [user, accounts, transactions, stats] = await Promise.all([
                    api.getCurrentUser(),
                    api.getBankAccounts(),
                    api.getTransactions({ limit: 5 }),
                    api.getStatistics()
                ]);

                const summary = {
                    user: user.success ? user.data : null,
                    accounts: accounts.success ? accounts.data : null,
                    transactions: transactions.success ? transactions.data : null,
                    statistics: stats.success ? stats.data : null,
                    timestamp: new Date().toISOString()
                };

                showResponse(summary, 'success');
            } catch (error) {
                showResponse(`❌ Грешка при обновяване: ${error.message}`, 'error');
            }
        }

        function showResponse(data, type = 'info') {
            const responseDiv = document.getElementById('apiResponse');
            const timestamp = new Date().toLocaleTimeString();
            
            let content;
            if (typeof data === 'string') {
                content = `[${timestamp}] ${data}`;
            } else {
                content = `[${timestamp}] ${JSON.stringify(data, null, 2)}`;
            }
            
            responseDiv.innerHTML = `<pre class="${type}">${content}</pre>`;
            responseDiv.scrollTop = responseDiv.scrollHeight;
        }

        function clearResponse() {
            document.getElementById('apiResponse').innerHTML = '<em>API отговорите ще се показват тук...</em>';
        }

        // Initialize on page load
        updateAuthStatus();
    </script>
</body>
</html>
