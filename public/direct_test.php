<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Controllers\UserSettingsController;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

echo "<!DOCTYPE html><html><head><title>Direct Test</title></head><body>";
echo "<h1>Direct Controller Test</h1>";

try {
    echo "<h2>1. Testing nextcloudSettings method</h2>";
    
    $controller = new UserSettingsController();
    
    // Capture output
    ob_start();
    $controller->nextcloudSettings();
    $output = ob_get_clean();
    
    echo "<p style='color: green;'>✅ nextcloudSettings method executed successfully</p>";
    echo "<p>Output length: " . strlen($output) . " characters</p>";
    
    // Show first 500 characters
    if (strlen($output) > 0) {
        echo "<h3>Output preview:</h3>";
        echo "<pre>" . htmlspecialchars(substr($output, 0, 500)) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error in nextcloudSettings: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

try {
    echo "<h2>2. Testing updateNextcloudSettings method</h2>";
    
    // Set up POST data
    $_POST = [
        '_token' => $_SESSION['csrf_token'],
        'server_url' => 'https://demo.nextcloud.com',
        'username' => 'testuser',
        'password' => 'testpass',
        'verify_ssl' => '1',
        'timeout' => '30',
        'default_folder' => '/ERP_Test',
        'auto_create_folders' => '1'
    ];
    
    $controller = new UserSettingsController();
    
    // Capture output
    ob_start();
    $controller->updateNextcloudSettings();
    $output = ob_get_clean();
    
    echo "<p style='color: green;'>✅ updateNextcloudSettings method executed successfully</p>";
    echo "<p>Output: " . htmlspecialchars($output) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error in updateNextcloudSettings: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

try {
    echo "<h2>3. Testing testNextcloudConnection method</h2>";
    
    // Set up POST data
    $_POST = [
        '_token' => $_SESSION['csrf_token']
    ];
    
    $controller = new UserSettingsController();
    
    // Capture output
    ob_start();
    $controller->testNextcloudConnection();
    $output = ob_get_clean();
    
    echo "<p style='color: green;'>✅ testNextcloudConnection method executed successfully</p>";
    echo "<p>Output: " . htmlspecialchars($output) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error in testNextcloudConnection: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</body></html>";
?>
