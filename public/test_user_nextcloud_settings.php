<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\UserNextcloudSettings;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

$currentUser = $app->getCurrentUser();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test User Nextcloud Settings</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .feature-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; }
        .feature-card h3 { margin-top: 0; color: #333; }
        .demo-actions { margin-top: 10px; }
        .demo-actions button, .demo-actions a { margin: 5px; padding: 8px 15px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <h1>🔧 User Nextcloud Settings Test</h1>
    
    <div class="test-section">
        <h2>👤 Current User Information</h2>
        <table border="1" cellpadding="5" cellspacing="0">
            <tr>
                <td><strong>User ID:</strong></td>
                <td><?= $currentUser['id'] ?></td>
            </tr>
            <tr>
                <td><strong>Username:</strong></td>
                <td><?= htmlspecialchars($currentUser['username']) ?></td>
            </tr>
            <tr>
                <td><strong>Full Name:</strong></td>
                <td><?= htmlspecialchars($currentUser['full_name']) ?></td>
            </tr>
            <tr>
                <td><strong>Email:</strong></td>
                <td><?= htmlspecialchars($currentUser['email']) ?></td>
            </tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>🔐 Permissions Check</h2>
        <table border="1" cellpadding="5" cellspacing="0">
            <tr>
                <th>Permission</th>
                <th>Status</th>
            </tr>
            <tr>
                <td>nextcloud.view_personal</td>
                <td class="<?= $app->hasPermission('nextcloud.view_personal') ? 'success' : 'error' ?>">
                    <?= $app->hasPermission('nextcloud.view_personal') ? '✅ Yes' : '❌ No' ?>
                </td>
            </tr>
            <tr>
                <td>nextcloud.manage_personal</td>
                <td class="<?= $app->hasPermission('nextcloud.manage_personal') ? 'success' : 'error' ?>">
                    <?= $app->hasPermission('nextcloud.manage_personal') ? '✅ Yes' : '❌ No' ?>
                </td>
            </tr>
            <tr>
                <td>nextcloud.test_connection</td>
                <td class="<?= $app->hasPermission('nextcloud.test_connection') ? 'success' : 'error' ?>">
                    <?= $app->hasPermission('nextcloud.test_connection') ? '✅ Yes' : '❌ No' ?>
                </td>
            </tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>⚙️ Current Nextcloud Settings</h2>
        <?php
        $userSettings = UserNextcloudSettings::getActiveForUser($currentUser['id']);
        
        if ($userSettings):
        ?>
            <table border="1" cellpadding="5" cellspacing="0">
                <tr>
                    <td><strong>Settings ID:</strong></td>
                    <td><?= $userSettings->id ?></td>
                </tr>
                <tr>
                    <td><strong>Server URL:</strong></td>
                    <td><?= htmlspecialchars($userSettings->server_url ?: 'Not set') ?></td>
                </tr>
                <tr>
                    <td><strong>Username:</strong></td>
                    <td><?= htmlspecialchars($userSettings->username ?: 'Not set') ?></td>
                </tr>
                <tr>
                    <td><strong>Has Password:</strong></td>
                    <td class="<?= !empty($userSettings->password) ? 'success' : 'error' ?>">
                        <?= !empty($userSettings->password) ? '✅ Yes' : '❌ No' ?>
                    </td>
                </tr>
                <tr>
                    <td><strong>Verify SSL:</strong></td>
                    <td><?= $userSettings->verify_ssl ? 'Yes' : 'No' ?></td>
                </tr>
                <tr>
                    <td><strong>Timeout:</strong></td>
                    <td><?= $userSettings->timeout ?> seconds</td>
                </tr>
                <tr>
                    <td><strong>Default Folder:</strong></td>
                    <td><?= htmlspecialchars($userSettings->default_folder) ?></td>
                </tr>
                <tr>
                    <td><strong>Auto Create Folders:</strong></td>
                    <td><?= $userSettings->auto_create_folders ? 'Yes' : 'No' ?></td>
                </tr>
                <tr>
                    <td><strong>Last Test Result:</strong></td>
                    <td class="<?= $userSettings->last_test_result === 'success' ? 'success' : ($userSettings->last_test_result === 'failed' ? 'error' : 'warning') ?>">
                        <?php
                        switch($userSettings->last_test_result) {
                            case 'success': echo '✅ Success'; break;
                            case 'failed': echo '❌ Failed'; break;
                            default: echo '⏳ Pending'; break;
                        }
                        ?>
                    </td>
                </tr>
                <?php if ($userSettings->last_tested_at): ?>
                <tr>
                    <td><strong>Last Tested:</strong></td>
                    <td><?= date('d.m.Y H:i:s', strtotime($userSettings->last_tested_at)) ?></td>
                </tr>
                <?php endif; ?>
                <?php if ($userSettings->last_error_message): ?>
                <tr>
                    <td><strong>Last Error:</strong></td>
                    <td class="error"><?= htmlspecialchars($userSettings->last_error_message) ?></td>
                </tr>
                <?php endif; ?>
                <tr>
                    <td><strong>Created:</strong></td>
                    <td><?= date('d.m.Y H:i:s', strtotime($userSettings->created_at)) ?></td>
                </tr>
                <tr>
                    <td><strong>Updated:</strong></td>
                    <td><?= date('d.m.Y H:i:s', strtotime($userSettings->updated_at)) ?></td>
                </tr>
            </table>
        <?php else: ?>
            <p class="warning">⚠️ No Nextcloud settings configured for this user</p>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>🧪 Functionality Tests</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <h3>👤 User Profile</h3>
                <p>Manage user profile and basic settings</p>
                <div class="demo-actions">
                    <a href="/admin/profile" class="btn-primary">Open Profile</a>
                </div>
            </div>
            
            <div class="feature-card">
                <h3>☁️ Nextcloud Settings</h3>
                <p>Configure personal Nextcloud connection</p>
                <div class="demo-actions">
                    <a href="/admin/profile/nextcloud-settings" class="btn-info">Manage Settings</a>
                </div>
            </div>
            
            <div class="feature-card">
                <h3>🔄 Test Connection</h3>
                <p>Test Nextcloud connection with current settings</p>
                <div class="demo-actions">
                    <button class="btn-warning" onclick="testConnection()">Test Connection</button>
                </div>
                <div id="testResult"></div>
            </div>
            
            <div class="feature-card">
                <h3>💾 Storage Access</h3>
                <p>Access file manager with personal settings</p>
                <div class="demo-actions">
                    <a href="/admin/storage" class="btn-success">Open Storage</a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📋 Integration Status</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <h3>✅ Completed Features</h3>
                <ul>
                    <li>✅ Database schema for user settings</li>
                    <li>✅ UserNextcloudSettings model</li>
                    <li>✅ UserSettingsController</li>
                    <li>✅ Profile management interface</li>
                    <li>✅ Nextcloud settings interface</li>
                    <li>✅ Modified NextcloudClient for user settings</li>
                    <li>✅ Storage integration with user settings</li>
                    <li>✅ Permission system integration</li>
                    <li>✅ Encrypted password storage</li>
                    <li>✅ Connection testing</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>🎯 Key Benefits</h3>
                <ul>
                    <li>🔐 Personal Nextcloud accounts per user</li>
                    <li>🔒 Encrypted password storage</li>
                    <li>⚙️ Individual configuration options</li>
                    <li>🔄 Connection testing and validation</li>
                    <li>📁 Custom default folders per user</li>
                    <li>🛡️ Permission-based access control</li>
                    <li>🎨 Integrated user interface</li>
                    <li>📊 Status tracking and error reporting</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🚀 Next Steps</h2>
        <div class="feature-card">
            <h3>To use personal Nextcloud settings:</h3>
            <ol>
                <li><strong>Configure your Nextcloud settings:</strong>
                    <ul>
                        <li>Go to <a href="/admin/profile/nextcloud-settings">Nextcloud Settings</a></li>
                        <li>Enter your Nextcloud server URL</li>
                        <li>Enter your username</li>
                        <li>Create and enter an App Password</li>
                    </ul>
                </li>
                <li><strong>Test the connection:</strong>
                    <ul>
                        <li>Use the "Test Connection" button</li>
                        <li>Verify successful connection</li>
                    </ul>
                </li>
                <li><strong>Use the Storage Manager:</strong>
                    <ul>
                        <li>Access <a href="/admin/storage">Storage/File Manager</a></li>
                        <li>Your personal Nextcloud will be used automatically</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>
    
    <div class="demo-actions" style="text-align: center; margin-top: 30px;">
        <a href="/admin/profile" class="btn-primary">👤 User Profile</a>
        <a href="/admin/profile/nextcloud-settings" class="btn-info">☁️ Nextcloud Settings</a>
        <a href="/admin/storage" class="btn-success">💾 Storage Manager</a>
        <a href="/admin" class="btn-secondary">🏠 Dashboard</a>
    </div>
    
    <script>
    function testConnection() {
        const result = document.getElementById('testResult');
        result.innerHTML = '<p class="info">🔄 Testing connection...</p>';
        
        fetch('/admin/profile/nextcloud-settings/test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: '_token=<?= $_SESSION['csrf_token'] ?? '' ?>'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                result.innerHTML = '<p class="success">✅ ' + data.message + '</p>';
                setTimeout(() => location.reload(), 2000);
            } else {
                result.innerHTML = '<p class="error">❌ ' + data.error + '</p>';
            }
        })
        .catch(error => {
            result.innerHTML = '<p class="error">❌ Network error: ' + error.message + '</p>';
        });
    }
    </script>
</body>
</html>
