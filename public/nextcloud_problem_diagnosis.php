<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\UserNextcloudSettings;
use Strix\ERP\Services\NextcloudClient;
use Strix\ERP\Controllers\StorageController;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

// Mock login
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['user'] = [
    'id' => 1,
    'username' => 'admin',
    'role' => 'admin',
    'permissions' => ['storage.view', 'storage.download', 'storage.edit']
];

echo "<!DOCTYPE html><html><head><title>Nextcloud Problem Diagnosis</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .section { border: 1px solid #ddd; margin: 15px 0; padding: 20px; border-radius: 8px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .critical { background: #f8d7da; border-color: #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0; }
    .solution { background: #d4edda; border-color: #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0; }
    .step { background: #e2e3e5; padding: 10px; margin: 5px 0; border-radius: 3px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    .btn { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; text-decoration: none; display: inline-block; }
    .btn-primary { background: #007bff; color: white; }
    .btn-success { background: #28a745; color: white; }
    .btn-warning { background: #ffc107; color: black; }
</style></head><body>";

echo "<h1>🔍 Nextcloud Problem Diagnosis</h1>";
echo "<p>This tool will help diagnose why Nextcloud file downloads are not working with your real server.</p>";

$problems = [];
$solutions = [];

// Step 1: Check user settings
echo "<div class='section'>";
echo "<h2>Step 1: User Settings Analysis</h2>";

try {
    $userSettings = UserNextcloudSettings::getActiveForUser(1);
    
    if (!$userSettings) {
        $problems[] = "No user Nextcloud settings found";
        $solutions[] = "Create user Nextcloud settings with your real server details";
        echo "<div class='critical'>";
        echo "<p class='error'>❌ CRITICAL: No user Nextcloud settings found</p>";
        echo "<p>The system is falling back to global configuration (.env file) which contains placeholder values.</p>";
        echo "</div>";
    } else {
        echo "<p class='success'>✅ User settings found</p>";
        
        // Check individual settings
        if (empty($userSettings->server_url) || in_array($userSettings->server_url, ['https://your-nextcloud-server.com'])) {
            $problems[] = "Invalid server URL in user settings";
            echo "<p class='error'>❌ Server URL is invalid or placeholder</p>";
        } else {
            echo "<p class='success'>✅ Server URL looks valid: " . htmlspecialchars($userSettings->server_url) . "</p>";
        }
        
        if (empty($userSettings->username) || in_array($userSettings->username, ['your-username'])) {
            $problems[] = "Invalid username in user settings";
            echo "<p class='error'>❌ Username is invalid or placeholder</p>";
        } else {
            echo "<p class='success'>✅ Username looks valid: " . htmlspecialchars($userSettings->username) . "</p>";
        }
        
        $decryptedPassword = $userSettings->getDecryptedPassword();
        if (empty($decryptedPassword) || in_array($decryptedPassword, ['your-app-password', 'your-password'])) {
            $problems[] = "Invalid password in user settings";
            echo "<p class='error'>❌ Password is invalid or placeholder</p>";
        } else {
            echo "<p class='success'>✅ Password is set (length: " . strlen($decryptedPassword) . " characters)</p>";
        }
        
        // Test connection
        echo "<h3>Connection Test:</h3>";
        $testResult = $userSettings->testConnection();
        if ($testResult['success']) {
            echo "<p class='success'>✅ Connection test successful</p>";
        } else {
            $problems[] = "Connection test failed: " . $testResult['error'];
            echo "<p class='error'>❌ Connection test failed: " . htmlspecialchars($testResult['error']) . "</p>";
        }
    }
    
} catch (Exception $e) {
    $problems[] = "Error checking user settings: " . $e->getMessage();
    echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Step 2: Check what client is being used
echo "<div class='section'>";
echo "<h2>Step 2: Client Selection Analysis</h2>";

try {
    $controller = new StorageController();
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('getNextcloudClient');
    $method->setAccessible(true);
    $client = $method->invoke($controller);
    
    $clientType = get_class($client);
    echo "<p><strong>Current client:</strong> " . $clientType . "</p>";
    
    if ($clientType === 'Strix\ERP\Services\MockNextcloudClient') {
        $problems[] = "System is using MockNextcloudClient instead of real client";
        echo "<div class='critical'>";
        echo "<p class='error'>❌ PROBLEM IDENTIFIED: Using MockNextcloudClient</p>";
        echo "<p>This means your real Nextcloud configuration is not being used.</p>";
        echo "</div>";
    } else {
        echo "<p class='success'>✅ Using real NextcloudClient</p>";
        
        if (method_exists($client, 'getDebugInfo')) {
            $debugInfo = $client->getDebugInfo();
            echo "<p><strong>Client configuration:</strong></p>";
            echo "<ul>";
            foreach ($debugInfo as $key => $value) {
                echo "<li><strong>" . ucfirst(str_replace('_', ' ', $key)) . ":</strong> " . htmlspecialchars($value) . "</li>";
            }
            echo "</ul>";
        }
    }
    
} catch (Exception $e) {
    $problems[] = "Error checking client: " . $e->getMessage();
    echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Step 3: Test file operations
if (isset($client) && get_class($client) === 'Strix\ERP\Services\NextcloudClient') {
    echo "<div class='section'>";
    echo "<h2>Step 3: File Operations Test</h2>";
    
    try {
        echo "<p class='info'>Testing directory listing...</p>";
        $files = $client->listDirectory('/');
        echo "<p class='success'>✅ Directory listing successful (" . count($files) . " items)</p>";
        
        // Test file download
        $testFile = null;
        foreach ($files as $file) {
            if (!$file['is_directory'] && $file['size'] > 0 && $file['size'] < 1024*1024) {
                $testFile = $file;
                break;
            }
        }
        
        if ($testFile) {
            echo "<p class='info'>Testing file download with: " . htmlspecialchars($testFile['name']) . "</p>";
            
            $content = $client->downloadFile($testFile['path']);
            if ($content !== false) {
                echo "<p class='success'>✅ File download successful (" . number_format(strlen($content)) . " bytes)</p>";
                
                if (strlen($content) === $testFile['size']) {
                    echo "<p class='success'>✅ Downloaded size matches expected size</p>";
                } else {
                    $problems[] = "Downloaded file size mismatch";
                    echo "<p class='warning'>⚠️ Size mismatch - expected: " . $testFile['size'] . ", got: " . strlen($content) . "</p>";
                }
            } else {
                $problems[] = "File download failed";
                echo "<p class='error'>❌ File download failed</p>";
            }
        } else {
            echo "<p class='warning'>⚠️ No suitable test file found</p>";
        }
        
    } catch (Exception $e) {
        $problems[] = "File operations failed: " . $e->getMessage();
        echo "<p class='error'>❌ File operations error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "</div>";
}

// Summary and solutions
echo "<div class='section'>";
echo "<h2>Summary & Solutions</h2>";

if (empty($problems)) {
    echo "<div class='solution'>";
    echo "<p class='success'>🎉 No problems detected! Your Nextcloud integration should be working correctly.</p>";
    echo "</div>";
} else {
    echo "<div class='critical'>";
    echo "<h3>Problems Identified:</h3>";
    echo "<ol>";
    foreach ($problems as $problem) {
        echo "<li>" . htmlspecialchars($problem) . "</li>";
    }
    echo "</ol>";
    echo "</div>";
    
    echo "<div class='solution'>";
    echo "<h3>🔧 Solutions:</h3>";
    
    if (in_array("No user Nextcloud settings found", $problems)) {
        echo "<div class='step'>";
        echo "<h4>1. Create User Nextcloud Settings</h4>";
        echo "<p>You need to create user-specific Nextcloud settings instead of relying on global configuration.</p>";
        echo "<p><a href='/create_test_nextcloud_settings.php' class='btn btn-primary'>Create Settings</a></p>";
        echo "</div>";
    }
    
    if (strpos(implode(' ', $problems), 'Invalid') !== false) {
        echo "<div class='step'>";
        echo "<h4>2. Update Invalid Settings</h4>";
        echo "<p>Your settings contain placeholder values. Update them with real Nextcloud server details:</p>";
        echo "<ul>";
        echo "<li><strong>Server URL:</strong> Your actual Nextcloud server (e.g., https://cloud.example.com)</li>";
        echo "<li><strong>Username:</strong> Your Nextcloud username</li>";
        echo "<li><strong>Password:</strong> Generate an app password in Nextcloud Settings > Security</li>";
        echo "</ul>";
        echo "<p><a href='/admin/user-settings' class='btn btn-warning'>Update Settings</a></p>";
        echo "</div>";
    }
    
    if (strpos(implode(' ', $problems), 'Connection test failed') !== false) {
        echo "<div class='step'>";
        echo "<h4>3. Fix Connection Issues</h4>";
        echo "<p>Check the following:</p>";
        echo "<ul>";
        echo "<li>Is your Nextcloud server accessible from this server?</li>";
        echo "<li>Are your credentials correct?</li>";
        echo "<li>Is SSL verification causing issues? (try disabling for testing)</li>";
        echo "<li>Check firewall settings</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    if (in_array("System is using MockNextcloudClient instead of real client", $problems)) {
        echo "<div class='step'>";
        echo "<h4>4. Force Real Client Usage</h4>";
        echo "<p>Once you have valid settings, you can force the system to use the real client:</p>";
        echo "<p><a href='/admin/storage?force_real=1' class='btn btn-success'>Force Real Client</a></p>";
        echo "</div>";
    }
    
    echo "</div>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>Quick Actions</h2>";
echo "<a href='/create_test_nextcloud_settings.php' class='btn btn-primary'>🔧 Create Settings</a>";
echo "<a href='/check_user_nextcloud_settings.php' class='btn btn-primary'>🔍 Check Settings</a>";
echo "<a href='/test_real_client.php' class='btn btn-primary'>🧪 Test Real Client</a>";
echo "<a href='/view_nextcloud_logs.php' class='btn btn-primary'>📋 View Logs</a>";
echo "<a href='/admin/storage' class='btn btn-primary'>📁 Open Storage</a>";
echo "</div>";

echo "</body></html>";
