<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

echo "<!DOCTYPE html><html><head><title>Nextcloud Logs</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .log-entry { margin: 5px 0; padding: 5px; border-radius: 3px; }
    .debug { background: #f0f0f0; }
    .info { background: #e8f4fd; }
    .warning { background: #fff3cd; }
    .error { background: #f8d7da; }
    pre { white-space: pre-wrap; word-wrap: break-word; }
    .controls { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }
    .clear-btn { background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 3px; cursor: pointer; }
    .refresh-btn { background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 3px; cursor: pointer; }
</style></head><body>";

echo "<h1>📋 Nextcloud Logs</h1>";

$logFile = __DIR__ . '/../logs/nextcloud.log';

// Handle clear logs
if (isset($_POST['clear_logs'])) {
    if (file_exists($logFile)) {
        file_put_contents($logFile, '');
        echo "<p style='color: green;'>✅ Logs cleared successfully</p>";
    }
}

echo "<div class='controls'>";
echo "<form method='POST' style='display: inline;'>";
echo "<button type='submit' name='clear_logs' class='clear-btn' onclick='return confirm(\"Are you sure you want to clear all logs?\")'>Clear Logs</button>";
echo "</form>";
echo " ";
echo "<button onclick='location.reload()' class='refresh-btn'>Refresh</button>";
echo " ";
echo "<a href='/test_real_client.php'>🧪 Test Real Client</a>";
echo " ";
echo "<a href='/debug_real_nextcloud.php'>🔧 Debug Nextcloud</a>";
echo "</div>";

if (file_exists($logFile)) {
    $logs = file_get_contents($logFile);
    
    if (!empty($logs)) {
        echo "<h2>Log Entries</h2>";
        
        $lines = explode("\n", $logs);
        $lines = array_reverse(array_filter($lines)); // Show newest first
        
        if (count($lines) > 100) {
            echo "<p><em>Showing last 100 entries (total: " . count($lines) . ")</em></p>";
            $lines = array_slice($lines, 0, 100);
        }
        
        foreach ($lines as $line) {
            if (empty(trim($line))) continue;
            
            // Parse log level
            $level = 'info';
            if (preg_match('/\[(\w+)\]/', $line, $matches)) {
                $level = strtolower($matches[1]);
            }
            
            echo "<div class='log-entry $level'>";
            echo "<pre>" . htmlspecialchars($line) . "</pre>";
            echo "</div>";
        }
        
    } else {
        echo "<p><em>Log file is empty</em></p>";
    }
    
} else {
    echo "<p><em>No log file found. Logs will be created when Nextcloud operations are performed.</em></p>";
}

echo "<hr>";
echo "<h2>🧪 Quick Tests</h2>";
echo "<p>Run these tests to generate log entries:</p>";
echo "<ul>";
echo "<li><a href='/test_real_client.php'>Test Real Nextcloud Client</a></li>";
echo "<li><a href='/debug_real_nextcloud.php'>Debug Nextcloud Configuration</a></li>";
echo "<li><a href='/admin/storage?force_real=1'>Open Storage with Real Client</a></li>";
echo "</ul>";

echo "</body></html>";
