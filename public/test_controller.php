<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Controllers\UserSettingsController;
use Strix\ERP\Models\UserNextcloudSettings;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Controller</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 Test Controller Loading</h1>
    
    <h2>Class Loading Tests</h2>
    
    <?php
    echo "<h3>1. Testing UserSettingsController</h3>";
    try {
        $controller = new UserSettingsController();
        echo "<p class='success'>✅ UserSettingsController loaded successfully</p>";
        echo "<p>Class: " . get_class($controller) . "</p>";
        echo "<p>Methods: " . implode(', ', get_class_methods($controller)) . "</p>";
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error loading UserSettingsController: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h3>2. Testing UserNextcloudSettings</h3>";
    try {
        $currentUser = $app->getCurrentUser();
        $settings = UserNextcloudSettings::getActiveForUser($currentUser['id']);
        echo "<p class='success'>✅ UserNextcloudSettings loaded successfully</p>";
        echo "<p>Settings found: " . ($settings ? 'Yes' : 'No') . "</p>";
        if ($settings) {
            echo "<pre>" . print_r($settings, true) . "</pre>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error with UserNextcloudSettings: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h3>3. Testing Database Connection</h3>";
    try {
        $sql = "SELECT COUNT(*) as count FROM user_nextcloud_settings";
        $result = \Strix\ERP\Core\Database::fetchOne($sql);
        echo "<p class='success'>✅ Database connection OK</p>";
        echo "<p>Total settings records: " . $result['count'] . "</p>";
    } catch (Exception $e) {
        echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h3>4. Testing Permissions</h3>";
    $permissions = [
        'nextcloud.view_personal',
        'nextcloud.manage_personal', 
        'nextcloud.test_connection'
    ];
    
    foreach ($permissions as $permission) {
        $hasPermission = $app->hasPermission($permission);
        $class = $hasPermission ? 'success' : 'error';
        $icon = $hasPermission ? '✅' : '❌';
        echo "<p class='$class'>$icon $permission</p>";
    }
    
    echo "<h3>5. Testing Router</h3>";
    try {
        $router = new \Strix\ERP\Core\Router();
        echo "<p class='success'>✅ Router loaded successfully</p>";
        
        // Test if we can add a route
        $router->get('/test', function() { return 'test'; });
        echo "<p class='success'>✅ Route registration works</p>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Router error: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h3>6. Testing Manual Controller Call</h3>";
    try {
        // Simulate a controller call
        $_POST['_token'] = $_SESSION['csrf_token'] ?? '';
        $_POST['server_url'] = 'https://demo.nextcloud.com';
        $_POST['username'] = 'testuser';
        $_POST['password'] = 'testpass';
        
        $controller = new UserSettingsController();
        
        // Check if method exists
        if (method_exists($controller, 'updateNextcloudSettings')) {
            echo "<p class='success'>✅ updateNextcloudSettings method exists</p>";
        } else {
            echo "<p class='error'>❌ updateNextcloudSettings method not found</p>";
        }
        
        if (method_exists($controller, 'testNextcloudConnection')) {
            echo "<p class='success'>✅ testNextcloudConnection method exists</p>";
        } else {
            echo "<p class='error'>❌ testNextcloudConnection method not found</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Controller test error: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h3>7. Testing CSRF Token</h3>";
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    echo "<p class='success'>✅ CSRF Token: " . $_SESSION['csrf_token'] . "</p>";
    
    echo "<h3>8. Testing Current User</h3>";
    $currentUser = $app->getCurrentUser();
    echo "<pre>" . print_r($currentUser, true) . "</pre>";
    ?>
    
    <h2>Manual API Test</h2>
    <button onclick="manualTest()">Test API Call</button>
    <div id="manualResult"></div>
    
    <script>
    function manualTest() {
        const result = document.getElementById('manualResult');
        result.innerHTML = '<p style="color: blue;">Testing...</p>';
        
        const formData = new FormData();
        formData.append('_token', '<?= $_SESSION['csrf_token'] ?? '' ?>');
        formData.append('server_url', 'https://demo.nextcloud.com');
        formData.append('username', 'testuser');
        formData.append('password', 'testpass');
        formData.append('verify_ssl', '1');
        formData.append('timeout', '30');
        formData.append('default_folder', '/ERP_Test');
        formData.append('auto_create_folders', '1');
        
        console.log('Sending request to /admin/profile/nextcloud-settings/update');
        
        fetch('/admin/profile/nextcloud-settings/update', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Response received:', response);
            console.log('Status:', response.status);
            console.log('Headers:', [...response.headers.entries()]);
            
            return response.text();
        })
        .then(text => {
            console.log('Response text:', text);
            
            try {
                const data = JSON.parse(text);
                result.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (e) {
                result.innerHTML = '<p style="color: red;">Invalid JSON response:</p><pre>' + text + '</pre>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            result.innerHTML = '<p style="color: red;">Error: ' + error.message + '</p>';
        });
    }
    </script>
</body>
</html>
