<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Services\NextcloudClient;
use Strix\ERP\Services\MockNextcloudClient;
use Strix\ERP\Models\UserNextcloudSettings;
use Strix\ERP\Controllers\StorageController;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

// Mock login
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['user'] = [
    'id' => 1,
    'username' => 'admin',
    'role' => 'admin',
    'permissions' => ['storage.view', 'storage.download', 'storage.edit', 'storage.delete', 'storage.upload', 'storage.create']
];

echo "<!DOCTYPE html><html><head><title>Nextcloud Diagnostics</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .section { border: 1px solid #ddd; margin: 15px 0; padding: 20px; border-radius: 8px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    .config-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    .config-table th, .config-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    .config-table th { background: #f8f9fa; }
    .test-results { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
    .action-buttons { margin: 20px 0; }
    .btn { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; text-decoration: none; display: inline-block; }
    .btn-primary { background: #007bff; color: white; }
    .btn-success { background: #28a745; color: white; }
    .btn-warning { background: #ffc107; color: black; }
    .btn-danger { background: #dc3545; color: white; }
</style></head><body>";

echo "<h1>🔍 Nextcloud Diagnostics</h1>";
echo "<p>Comprehensive diagnostic tool for Nextcloud integration issues.</p>";

// Section 1: Environment Check
echo "<div class='section'>";
echo "<h2>1. Environment Check</h2>";

$envVars = [
    'NEXTCLOUD_URL' => $_ENV['NEXTCLOUD_URL'] ?? 'not set',
    'NEXTCLOUD_USERNAME' => $_ENV['NEXTCLOUD_USERNAME'] ?? 'not set',
    'NEXTCLOUD_PASSWORD' => !empty($_ENV['NEXTCLOUD_PASSWORD']) ? 'set (hidden)' : 'not set',
    'NEXTCLOUD_AUTH_METHOD' => $_ENV['NEXTCLOUD_AUTH_METHOD'] ?? 'not set',
    'NEXTCLOUD_VERIFY_SSL' => $_ENV['NEXTCLOUD_VERIFY_SSL'] ?? 'not set',
    'NEXTCLOUD_LOGGING' => $_ENV['NEXTCLOUD_LOGGING'] ?? 'not set',
    'NEXTCLOUD_LOG_LEVEL' => $_ENV['NEXTCLOUD_LOG_LEVEL'] ?? 'not set'
];

echo "<table class='config-table'>";
echo "<tr><th>Environment Variable</th><th>Value</th><th>Status</th></tr>";
foreach ($envVars as $var => $value) {
    $status = 'info';
    $statusText = 'OK';
    
    if ($value === 'not set') {
        $status = 'warning';
        $statusText = 'Not Set';
    } elseif (in_array($value, ['https://your-nextcloud-server.com', 'your-username', 'your-app-password'])) {
        $status = 'error';
        $statusText = 'Placeholder';
    }
    
    echo "<tr>";
    echo "<td><strong>$var</strong></td>";
    echo "<td>" . htmlspecialchars($value) . "</td>";
    echo "<td class='$status'>$statusText</td>";
    echo "</tr>";
}
echo "</table>";

echo "</div>";

// Section 2: Configuration Analysis
echo "<div class='section'>";
echo "<h2>2. Configuration Analysis</h2>";

$config = require __DIR__ . '/../config/nextcloud.php';

echo "<h3>Global Configuration:</h3>";
echo "<table class='config-table'>";
echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";

$configChecks = [
    'Server URL' => $config['server']['url'],
    'WebDAV Path' => $config['server']['webdav_path'],
    'Username' => $config['auth']['username'],
    'Password' => !empty($config['auth']['password']) ? 'set (hidden)' : 'not set',
    'Timeout' => $config['server']['timeout'] . 's',
    'Verify SSL' => $config['server']['verify_ssl'] ? 'Yes' : 'No',
    'Logging Enabled' => $config['logging']['enabled'] ? 'Yes' : 'No',
    'Log Level' => $config['logging']['level']
];

foreach ($configChecks as $setting => $value) {
    $status = 'info';
    $statusText = 'OK';
    
    if (in_array($value, ['not set', 'https://your-nextcloud-server.com', 'your-username'])) {
        $status = 'error';
        $statusText = 'Invalid';
    }
    
    echo "<tr>";
    echo "<td><strong>$setting</strong></td>";
    echo "<td>" . htmlspecialchars($value) . "</td>";
    echo "<td class='$status'>$statusText</td>";
    echo "</tr>";
}
echo "</table>";

echo "</div>";

// Section 3: Client Creation Test
echo "<div class='section'>";
echo "<h2>3. Client Creation Test</h2>";

echo "<div class='test-results'>";
echo "<h3>Mock Client Test:</h3>";
try {
    $mockClient = new MockNextcloudClient();
    echo "<p class='success'>✅ MockNextcloudClient created successfully</p>";
    
    $mockFiles = $mockClient->listDirectory('/');
    echo "<p class='success'>✅ Mock client can list directories (" . count($mockFiles) . " items)</p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Mock client failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h3>Real Client Test:</h3>";
try {
    $realClient = new NextcloudClient();
    echo "<p class='success'>✅ NextcloudClient created successfully</p>";
    
    if (method_exists($realClient, 'getDebugInfo')) {
        $debugInfo = $realClient->getDebugInfo();
        echo "<p><strong>Client Configuration:</strong></p>";
        echo "<ul>";
        foreach ($debugInfo as $key => $value) {
            echo "<li><strong>" . ucfirst(str_replace('_', ' ', $key)) . ":</strong> " . htmlspecialchars($value) . "</li>";
        }
        echo "</ul>";
    }
    
    // Test connection
    try {
        $realFiles = $realClient->listDirectory('/');
        echo "<p class='success'>✅ Real client can connect and list directories (" . count($realFiles) . " items)</p>";
        
        // Test a file operation
        if (!empty($realFiles)) {
            $testFile = null;
            foreach ($realFiles as $file) {
                if (!$file['is_directory'] && $file['size'] > 0 && $file['size'] < 1024*1024) {
                    $testFile = $file;
                    break;
                }
            }
            
            if ($testFile) {
                echo "<p class='info'>Testing with file: " . htmlspecialchars($testFile['name']) . "</p>";
                
                $exists = $realClient->exists($testFile['path']);
                echo "<p class='" . ($exists ? 'success' : 'error') . "'>";
                echo ($exists ? "✅" : "❌") . " File exists check: " . ($exists ? "PASS" : "FAIL");
                echo "</p>";
                
                if ($exists) {
                    $content = $realClient->downloadFile($testFile['path']);
                    if ($content !== false) {
                        echo "<p class='success'>✅ File download: PASS (" . number_format(strlen($content)) . " bytes)</p>";
                    } else {
                        echo "<p class='error'>❌ File download: FAIL</p>";
                    }
                }
            }
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Real client connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Real client creation failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

echo "</div>";

// Section 4: Controller Test
echo "<div class='section'>";
echo "<h2>4. Controller Integration Test</h2>";

echo "<div class='test-results'>";
try {
    $controller = new StorageController();
    echo "<p class='success'>✅ StorageController created successfully</p>";
    
    // Test auto-detection
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('getNextcloudClient');
    $method->setAccessible(true);
    
    $autoClient = $method->invoke($controller);
    $autoClientType = get_class($autoClient);
    echo "<p><strong>Auto-detected client:</strong> " . $autoClientType . "</p>";
    
    if ($autoClientType === 'Strix\ERP\Services\NextcloudClient') {
        echo "<p class='success'>✅ Controller is using real NextcloudClient</p>";
    } else {
        echo "<p class='warning'>⚠️ Controller is using MockNextcloudClient (fallback mode)</p>";
    }
    
    // Test forced real client
    $_GET['force_real'] = true;
    $controller = new StorageController(); // Create new instance
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('getNextcloudClient');
    $method->setAccessible(true);
    
    $forcedClient = $method->invoke($controller);
    $forcedClientType = get_class($forcedClient);
    echo "<p><strong>Forced real client:</strong> " . $forcedClientType . "</p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Controller test failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

echo "</div>";

// Section 5: Recommendations
echo "<div class='section'>";
echo "<h2>5. Recommendations</h2>";

$hasValidConfig = !in_array($config['server']['url'], ['https://your-nextcloud-server.com', '']) &&
                  !in_array($config['auth']['username'], ['your-username', '']) &&
                  !in_array($config['auth']['password'], ['your-app-password', 'your-password', '']);

if ($hasValidConfig) {
    echo "<p class='success'>✅ Your configuration appears to be valid for real Nextcloud usage.</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>Test the real client with your actual Nextcloud server</li>";
    echo "<li>Check the logs for any connection issues</li>";
    echo "<li>Verify your Nextcloud server is accessible from this server</li>";
    echo "</ul>";
} else {
    echo "<p class='warning'>⚠️ Your configuration contains placeholder values.</p>";
    echo "<p><strong>To use real Nextcloud:</strong></p>";
    echo "<ul>";
    echo "<li>Update your .env file with real Nextcloud server details</li>";
    echo "<li>Set NEXTCLOUD_URL to your actual Nextcloud server URL</li>";
    echo "<li>Set NEXTCLOUD_USERNAME to your Nextcloud username</li>";
    echo "<li>Set NEXTCLOUD_PASSWORD to your Nextcloud app password</li>";
    echo "</ul>";
}

echo "</div>";

// Action Buttons
echo "<div class='action-buttons'>";
echo "<h2>Quick Actions</h2>";
echo "<a href='/test_real_client.php' class='btn btn-primary'>🧪 Test Real Client</a>";
echo "<a href='/view_nextcloud_logs.php' class='btn btn-success'>📋 View Logs</a>";
echo "<a href='/admin/storage' class='btn btn-warning'>📁 Open Storage (Auto)</a>";
echo "<a href='/admin/storage?force_real=1' class='btn btn-danger'>🌐 Force Real Client</a>";
echo "</div>";

echo "</body></html>";
