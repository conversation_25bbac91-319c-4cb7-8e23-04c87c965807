<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Controllers\TaskController;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

echo "<h1>Test Tasks Page</h1>";

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

echo "<h2>Testing TaskController::index()</h2>";

try {
    // Simulate the request
    $_GET = []; // Clear any existing GET parameters
    
    $controller = new TaskController();
    
    // This should work without errors now
    ob_start();
    $controller->index();
    $output = ob_get_clean();
    
    echo "<p style='color: green;'>✅ TaskController::index() executed successfully!</p>";
    echo "<p>Output length: " . strlen($output) . " characters</p>";
    
    if (strlen($output) > 0) {
        echo "<p>✅ Page content generated</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ No output generated (might be a redirect)</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Actions:</h2>";
echo "<p><a href='/admin/tasks'>Go to Tasks Page</a></p>";
echo "<p><a href='/admin/tasks/create'>Create New Task</a></p>";
echo "<p><a href='/admin'>Back to Dashboard</a></p>";
