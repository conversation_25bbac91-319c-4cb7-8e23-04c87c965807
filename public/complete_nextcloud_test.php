<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Complete Nextcloud Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        button { margin: 5px; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-info { background: #17a2b8; color: white; }
        #result { margin-top: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="url"], input[type="password"], input[type="number"] {
            width: 100%;
            max-width: 400px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .checkbox-group { display: flex; align-items: center; gap: 10px; }
    </style>
</head>
<body>
    <h1>🧪 Complete Nextcloud Test</h1>
    
    <p><strong>CSRF Token:</strong> <code><?= $_SESSION['csrf_token'] ?></code></p>
    
    <h2>Nextcloud Settings Form</h2>
    <form id="nextcloudForm">
        <input type="hidden" name="_token" value="<?= $_SESSION['csrf_token'] ?>">
        
        <div class="form-group">
            <label for="server_url">Server URL *</label>
            <input type="url" id="server_url" name="server_url" value="https://demo.nextcloud.com" required>
        </div>
        
        <div class="form-group">
            <label for="username">Username *</label>
            <input type="text" id="username" name="username" value="testuser" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password *</label>
            <input type="password" id="password" name="password" value="testpass" required>
        </div>
        
        <div class="form-group">
            <label for="timeout">Timeout (seconds)</label>
            <input type="number" id="timeout" name="timeout" value="30" min="5" max="300">
        </div>
        
        <div class="form-group">
            <label for="default_folder">Default Folder</label>
            <input type="text" id="default_folder" name="default_folder" value="/ERP_Files">
        </div>
        
        <div class="form-group">
            <div class="checkbox-group">
                <input type="checkbox" id="verify_ssl" name="verify_ssl" value="1" checked>
                <label for="verify_ssl">Verify SSL Certificate</label>
            </div>
        </div>
        
        <div class="form-group">
            <div class="checkbox-group">
                <input type="checkbox" id="auto_create_folders" name="auto_create_folders" value="1" checked>
                <label for="auto_create_folders">Auto Create Folders</label>
            </div>
        </div>
        
        <div class="form-group">
            <button type="button" class="btn-warning" onclick="testBeforeSave()">🔄 Test Connection (Before Save)</button>
            <button type="submit" class="btn-success">💾 Save Settings</button>
            <button type="button" class="btn-info" onclick="testAfterSave()">🔄 Test Saved Settings</button>
            <button type="button" class="btn-primary" onclick="getSettings()">📋 Get Current Settings</button>
        </div>
    </form>
    
    <div id="result"></div>
    
    <h2>Quick Actions</h2>
    <p>
        <a href="/admin/profile/nextcloud-settings" class="btn-primary">Open Nextcloud Settings Page</a>
        <a href="/admin/storage" class="btn-success">Open Storage Manager</a>
        <a href="/admin/profile" class="btn-info">Open User Profile</a>
    </p>
    
    <script>
    // Form submission for saving
    document.getElementById('nextcloudForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const result = document.getElementById('result');
        result.innerHTML = '<p class="info">💾 Saving settings...</p>';
        
        const formData = new FormData(this);
        
        console.log('Saving settings...');
        console.log('FormData contents:');
        for (let [key, value] of formData.entries()) {
            console.log(key + ': ' + value);
        }
        
        fetch('/admin/profile/nextcloud-settings/update', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Save response:', response);
            return response.json();
        })
        .then(data => {
            console.log('Save data:', data);
            
            if (data.success) {
                result.innerHTML = '<p class="success">✅ ' + data.message + '</p>';
            } else {
                result.innerHTML = '<p class="error">❌ ' + data.error + '</p>';
            }
        })
        .catch(error => {
            console.error('Save error:', error);
            result.innerHTML = '<p class="error">❌ Save error: ' + error.message + '</p>';
        });
    });
    
    // Test connection before saving (with form data)
    function testBeforeSave() {
        const result = document.getElementById('result');
        result.innerHTML = '<p class="info">🔄 Testing connection with form data...</p>';
        
        const form = document.getElementById('nextcloudForm');
        const formData = new FormData(form);
        
        console.log('Testing before save...');
        console.log('Test FormData contents:');
        for (let [key, value] of formData.entries()) {
            console.log(key + ': ' + value);
        }
        
        fetch('/admin/profile/nextcloud-settings/test', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Test response:', response);
            return response.json();
        })
        .then(data => {
            console.log('Test data:', data);
            
            if (data.success) {
                result.innerHTML = '<p class="success">✅ ' + data.message + '</p>';
            } else {
                result.innerHTML = '<p class="error">❌ ' + data.error + '</p>';
            }
        })
        .catch(error => {
            console.error('Test error:', error);
            result.innerHTML = '<p class="error">❌ Test error: ' + error.message + '</p>';
        });
    }
    
    // Test connection with saved settings
    function testAfterSave() {
        const result = document.getElementById('result');
        result.innerHTML = '<p class="info">🔄 Testing saved settings...</p>';
        
        const formData = new FormData();
        formData.append('_token', '<?= $_SESSION['csrf_token'] ?>');
        
        console.log('Testing saved settings...');
        
        fetch('/admin/profile/nextcloud-settings/test', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Test saved response:', response);
            return response.json();
        })
        .then(data => {
            console.log('Test saved data:', data);
            
            if (data.success) {
                result.innerHTML = '<p class="success">✅ ' + data.message + '</p>';
            } else {
                result.innerHTML = '<p class="error">❌ ' + data.error + '</p>';
            }
        })
        .catch(error => {
            console.error('Test saved error:', error);
            result.innerHTML = '<p class="error">❌ Test saved error: ' + error.message + '</p>';
        });
    }
    
    // Get current settings
    function getSettings() {
        const result = document.getElementById('result');
        result.innerHTML = '<p class="info">📋 Getting current settings...</p>';
        
        console.log('Getting settings...');
        
        fetch('/admin/profile/nextcloud-settings/get', {
            method: 'GET'
        })
        .then(response => {
            console.log('Get response:', response);
            return response.json();
        })
        .then(data => {
            console.log('Get data:', data);
            result.innerHTML = '<p class="success">✅ Settings retrieved</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            console.error('Get error:', error);
            result.innerHTML = '<p class="error">❌ Get error: ' + error.message + '</p>';
        });
    }
    </script>
</body>
</html>
