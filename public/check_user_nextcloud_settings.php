<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\UserNextcloudSettings;
use Strix\ERP\Services\NextcloudClient;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

// Mock login
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['user'] = [
    'id' => 1,
    'username' => 'admin',
    'role' => 'admin',
    'permissions' => ['storage.view', 'storage.download', 'storage.edit']
];

echo "<!DOCTYPE html><html><head><title>Check User Nextcloud Settings</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .section { border: 1px solid #ddd; margin: 15px 0; padding: 20px; border-radius: 8px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    .config-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    .config-table th, .config-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    .config-table th { background: #f8f9fa; }
    .btn { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; text-decoration: none; display: inline-block; }
    .btn-primary { background: #007bff; color: white; }
</style></head><body>";

echo "<h1>🔍 Check User Nextcloud Settings</h1>";

// Section 1: Check current user settings
echo "<div class='section'>";
echo "<h2>1. Current User Settings (ID: 1)</h2>";

try {
    $userSettings = UserNextcloudSettings::getActiveForUser(1);
    
    if ($userSettings) {
        echo "<p class='success'>✅ Found active Nextcloud settings for user</p>";
        
        echo "<table class='config-table'>";
        echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";
        
        $settings = [
            'ID' => $userSettings->id,
            'Server URL' => $userSettings->server_url,
            'Username' => $userSettings->username,
            'Has Password' => !empty($userSettings->password) ? 'Yes' : 'No',
            'Verify SSL' => $userSettings->verify_ssl ? 'Yes' : 'No',
            'Timeout' => $userSettings->timeout . 's',
            'Default Folder' => $userSettings->default_folder,
            'Auto Create Folders' => $userSettings->auto_create_folders ? 'Yes' : 'No',
            'Is Active' => $userSettings->is_active ? 'Yes' : 'No',
            'Last Tested' => $userSettings->last_tested_at ?: 'Never',
            'Last Test Result' => $userSettings->last_test_result ?: 'None'
        ];
        
        foreach ($settings as $key => $value) {
            $status = 'info';
            $statusText = 'OK';
            
            if ($key === 'Server URL' && (empty($value) || in_array($value, ['https://your-nextcloud-server.com']))) {
                $status = 'error';
                $statusText = 'Invalid';
            } elseif ($key === 'Username' && (empty($value) || in_array($value, ['your-username']))) {
                $status = 'error';
                $statusText = 'Invalid';
            } elseif ($key === 'Has Password' && $value === 'No') {
                $status = 'error';
                $statusText = 'Missing';
            } elseif ($key === 'Is Active' && $value === 'No') {
                $status = 'warning';
                $statusText = 'Inactive';
            }
            
            echo "<tr>";
            echo "<td><strong>$key</strong></td>";
            echo "<td>" . htmlspecialchars($value) . "</td>";
            echo "<td class='$status'>$statusText</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Test password decryption
        echo "<h3>Password Decryption Test:</h3>";
        $decryptedPassword = $userSettings->getDecryptedPassword();
        if ($decryptedPassword) {
            echo "<p class='success'>✅ Password decryption successful</p>";
            echo "<p><strong>Decrypted password length:</strong> " . strlen($decryptedPassword) . " characters</p>";
        } else {
            echo "<p class='error'>❌ Password decryption failed</p>";
        }
        
        // Test connection
        echo "<h3>Connection Test:</h3>";
        try {
            $testResult = $userSettings->testConnection();
            if ($testResult['success']) {
                echo "<p class='success'>✅ Connection test successful</p>";
                echo "<p><strong>HTTP Code:</strong> " . $testResult['http_code'] . "</p>";
            } else {
                echo "<p class='error'>❌ Connection test failed</p>";
                echo "<p><strong>Error:</strong> " . htmlspecialchars($testResult['error']) . "</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ Connection test error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        // Test NextcloudClient creation
        echo "<h3>NextcloudClient Creation Test:</h3>";
        try {
            $client = new NextcloudClient($userSettings);
            echo "<p class='success'>✅ NextcloudClient created successfully with user settings</p>";
            
            if (method_exists($client, 'getDebugInfo')) {
                $debugInfo = $client->getDebugInfo();
                echo "<p><strong>Client Configuration:</strong></p>";
                echo "<ul>";
                foreach ($debugInfo as $key => $value) {
                    echo "<li><strong>" . ucfirst(str_replace('_', ' ', $key)) . ":</strong> " . htmlspecialchars($value) . "</li>";
                }
                echo "</ul>";
            }
            
            // Test listing directory
            try {
                $files = $client->listDirectory('/');
                echo "<p class='success'>✅ Directory listing successful (" . count($files) . " items)</p>";
                
                if (!empty($files)) {
                    echo "<p><strong>Sample files:</strong></p>";
                    echo "<ul>";
                    foreach (array_slice($files, 0, 5) as $file) {
                        $icon = $file['is_directory'] ? '📁' : '📄';
                        echo "<li>$icon " . htmlspecialchars($file['name']) . "</li>";
                    }
                    if (count($files) > 5) {
                        echo "<li>... and " . (count($files) - 5) . " more items</li>";
                    }
                    echo "</ul>";
                    
                    // Test file download
                    $testFile = null;
                    foreach ($files as $file) {
                        if (!$file['is_directory'] && $file['size'] > 0 && $file['size'] < 1024*1024) {
                            $testFile = $file;
                            break;
                        }
                    }
                    
                    if ($testFile) {
                        echo "<h4>File Download Test:</h4>";
                        echo "<p class='info'>Testing with: " . htmlspecialchars($testFile['name']) . " (" . number_format($testFile['size']) . " bytes)</p>";
                        
                        try {
                            $content = $client->downloadFile($testFile['path']);
                            if ($content !== false) {
                                echo "<p class='success'>✅ File download successful</p>";
                                echo "<p><strong>Downloaded:</strong> " . number_format(strlen($content)) . " bytes</p>";
                                
                                if (strlen($content) === $testFile['size']) {
                                    echo "<p class='success'>✅ Size matches perfectly!</p>";
                                } else {
                                    echo "<p class='warning'>⚠️ Size mismatch (expected: " . $testFile['size'] . ", got: " . strlen($content) . ")</p>";
                                }
                            } else {
                                echo "<p class='error'>❌ File download failed</p>";
                            }
                        } catch (Exception $e) {
                            echo "<p class='error'>❌ File download error: " . htmlspecialchars($e->getMessage()) . "</p>";
                        }
                    }
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>❌ Directory listing failed: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ NextcloudClient creation failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
    } else {
        echo "<p class='warning'>⚠️ No active Nextcloud settings found for user</p>";
        
        // Check all settings
        try {
            $allSettings = UserNextcloudSettings::getAllForUser(1);
            echo "<p><strong>Total settings for user:</strong> " . count($allSettings) . "</p>";
            
            if (!empty($allSettings)) {
                echo "<table class='config-table'>";
                echo "<tr><th>ID</th><th>Server URL</th><th>Username</th><th>Active</th><th>Created</th></tr>";
                foreach ($allSettings as $setting) {
                    echo "<tr>";
                    echo "<td>" . $setting->id . "</td>";
                    echo "<td>" . htmlspecialchars($setting->server_url) . "</td>";
                    echo "<td>" . htmlspecialchars($setting->username) . "</td>";
                    echo "<td>" . ($setting->is_active ? 'Yes' : 'No') . "</td>";
                    echo "<td>" . $setting->created_at . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ Error checking all settings: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error checking user settings: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Section 2: Global vs User Settings Comparison
echo "<div class='section'>";
echo "<h2>2. Global vs User Settings</h2>";

$globalConfig = require __DIR__ . '/../config/nextcloud.php';

echo "<table class='config-table'>";
echo "<tr><th>Setting</th><th>Global (.env)</th><th>User (Database)</th><th>Which is Used?</th></tr>";

$comparisons = [
    'Server URL' => [
        'global' => $globalConfig['server']['url'],
        'user' => $userSettings ? $userSettings->server_url : 'N/A'
    ],
    'Username' => [
        'global' => $globalConfig['auth']['username'],
        'user' => $userSettings ? $userSettings->username : 'N/A'
    ],
    'Has Password' => [
        'global' => !empty($globalConfig['auth']['password']) ? 'Yes' : 'No',
        'user' => $userSettings && !empty($userSettings->password) ? 'Yes' : 'No'
    ],
    'Timeout' => [
        'global' => $globalConfig['server']['timeout'] . 's',
        'user' => $userSettings ? $userSettings->timeout . 's' : 'N/A'
    ]
];

foreach ($comparisons as $setting => $values) {
    $whichUsed = 'Global';
    if ($userSettings && !empty($values['user']) && $values['user'] !== 'N/A') {
        $whichUsed = 'User';
    }
    
    echo "<tr>";
    echo "<td><strong>$setting</strong></td>";
    echo "<td>" . htmlspecialchars($values['global']) . "</td>";
    echo "<td>" . htmlspecialchars($values['user']) . "</td>";
    echo "<td class='" . ($whichUsed === 'User' ? 'success' : 'warning') . "'>$whichUsed</td>";
    echo "</tr>";
}
echo "</table>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>3. Quick Actions</h2>";
echo "<a href='/admin/user-settings' class='btn btn-primary'>📝 Manage User Settings</a>";
echo "<a href='/admin/storage' class='btn btn-primary'>📁 Open Storage</a>";
echo "<a href='/view_nextcloud_logs.php' class='btn btn-primary'>📋 View Logs</a>";
echo "</div>";

echo "</body></html>";
