<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

// Mock login
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';

// Simulate download request
$_GET['path'] = '/Photos/4K_B.jpg';

echo "<!DOCTYPE html><html><head><title>Test Download</title></head><body>";
echo "<h1>🔽 Test Download</h1>";

echo "<p><strong>Testing download of:</strong> " . htmlspecialchars($_GET['path']) . "</p>";

try {
    // Create controller and test download
    $controller = new \Strix\ERP\Controllers\StorageController();
    
    // Capture output
    ob_start();
    $controller->download();
    $output = ob_get_contents();
    ob_end_clean();
    
    if (!empty($output)) {
        echo "<p style='color: green;'>✅ Download successful</p>";
        echo "<p><strong>Content length:</strong> " . strlen($output) . " bytes</p>";
        echo "<p><strong>Content type:</strong> " . (function_exists('mime_content_type') ? mime_content_type($output) : 'unknown') . "</p>";
        echo "<p><strong>Content preview:</strong> " . htmlspecialchars(substr($output, 0, 200)) . "...</p>";
    } else {
        echo "<p style='color: red;'>❌ Download failed - no content</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='/admin/storage'>← Back to Storage</a></p>";
echo "<p><a href='/test_storage_operations.php'>🧪 Run Storage Tests</a></p>";

echo "</body></html>";
