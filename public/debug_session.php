<?php

/**
 * Debug session information
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

header('Content-Type: text/html; charset=UTF-8');

try {
    echo "<h1>🔍 Debug Session Information</h1>";
    
    // Initialize application
    $app = Application::getInstance();
    
    echo "<h2>1. Session Information</h2>";
    echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
    echo "<p><strong>Session Status:</strong> " . session_status() . "</p>";
    echo "<p><strong>Session Name:</strong> " . session_name() . "</p>";
    
    echo "<h3>Session Data:</h3>";
    echo "<pre>" . print_r($_SESSION, true) . "</pre>";
    
    echo "<h2>2. Authentication Status</h2>";
    $isLoggedIn = $app->isLoggedIn();
    echo "<p><strong>Is Logged In:</strong> " . ($isLoggedIn ? 'Yes' : 'No') . "</p>";
    
    if ($isLoggedIn) {
        $currentUser = $app->getCurrentUser();
        echo "<h3>Current User:</h3>";
        echo "<pre>" . print_r($currentUser, true) . "</pre>";
        
        echo "<h3>Task Permissions:</h3>";
        $taskPermissions = ['tasks.view', 'tasks.create', 'tasks.edit', 'tasks.delete'];
        foreach ($taskPermissions as $permission) {
            $hasPermission = $app->hasPermission($permission);
            echo "<p><strong>$permission:</strong> " . ($hasPermission ? '✓' : '❌') . "</p>";
        }
    } else {
        echo "<p style='color: red;'>User is not logged in</p>";
        echo "<p><a href='/login'>Go to Login</a></p>";
    }
    
    echo "<h2>3. Cookies</h2>";
    echo "<pre>" . print_r($_COOKIE, true) . "</pre>";
    
    echo "<h2>4. Server Information</h2>";
    echo "<p><strong>REQUEST_URI:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";
    echo "<p><strong>HTTP_HOST:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
    echo "<p><strong>SERVER_NAME:</strong> " . $_SERVER['SERVER_NAME'] . "</p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>Error</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
