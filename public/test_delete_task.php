<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Delete Task</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test Delete Task</h1>
    
    <p>This page tests the delete functionality for tasks.</p>
    
    <form id="deleteForm">
        <label for="taskId">Task ID to delete:</label>
        <input type="number" id="taskId" value="1" min="1">
        <button type="submit">Delete Task</button>
    </form>
    
    <div id="result" style="margin-top: 20px;"></div>
    
    <script>
    $('#deleteForm').on('submit', function(e) {
        e.preventDefault();
        
        const taskId = $('#taskId').val();
        const csrfToken = '<?= $_SESSION['csrf_token'] ?? bin2hex(random_bytes(32)) ?>';

        // Generate CSRF token if not exists
        <?php if (!isset($_SESSION['csrf_token'])): ?>
            <?php $_SESSION['csrf_token'] = bin2hex(random_bytes(32)); ?>
        <?php endif; ?>
        
        $('#result').html('<p>Deleting task ' + taskId + '...</p>');
        
        $.ajax({
            url: '/admin/tasks/' + taskId,
            method: 'POST',
            data: {
                '_method': 'DELETE',
                '_token': csrfToken
            },
            dataType: 'json',
            success: function(response) {
                $('#result').html('<p style="color: green;">✅ ' + response.message + '</p>');
            },
            error: function(xhr) {
                let errorMsg = 'Unknown error';
                try {
                    const response = JSON.parse(xhr.responseText);
                    errorMsg = response.error || errorMsg;
                } catch (e) {
                    errorMsg = xhr.responseText || errorMsg;
                }
                $('#result').html('<p style="color: red;">❌ Error: ' + errorMsg + '</p>');
            }
        });
    });
    </script>
    
    <h2>Actions:</h2>
    <p><a href="/admin/tasks">View All Tasks</a></p>
    <p><a href="/admin/tasks/create">Create New Task</a></p>
</body>
</html>
