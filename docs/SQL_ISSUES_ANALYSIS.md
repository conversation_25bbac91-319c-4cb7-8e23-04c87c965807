# 🔍 SQL Проблеми и Оптимизации - Анализ

## ❌ Открити проблеми

### 1. **UserController::index() - N+1 проблем**

**Проблем:**
```php
// UserController.php:26-28
$users = User::all();  // 1 заявка за всички потребители
$totalUsers = count($users);  // Зарежда всички записи в паметта
$users = array_slice($users, ($page - 1) * $perPage, $perPage);  // Филтрира в PHP
```

**Проблеми:**
- Зарежда ВСИЧКИ потребители в паметта
- Прави пагинация в PHP вместо в SQL
- Неефективно за големи таблици

**Решение:**
```php
// Оптимизирана версия
public function index(): void
{
    $search = $this->getInput('search', '');
    $page = max(1, (int) $this->getInput('page', 1));
    $perPage = 20;
    
    $whereClause = '';
    $params = [];
    
    if ($search) {
        $whereClause = "WHERE (first_name LIKE ? OR last_name LIKE ? OR username LIKE ? OR email LIKE ?)";
        $searchTerm = "%$search%";
        $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
    }
    
    // Count query
    $totalUsers = (int) Database::fetchColumn(
        "SELECT COUNT(*) FROM users $whereClause", 
        $params
    );
    
    // Data query with pagination
    $offset = ($page - 1) * $perPage;
    $users = Database::fetchAll(
        "SELECT * FROM users $whereClause ORDER BY created_at DESC LIMIT $perPage OFFSET $offset",
        $params
    );
    
    // Convert to User objects
    $userObjects = [];
    foreach ($users as $userData) {
        $user = new User();
        $user->fill($userData);
        $userObjects[] = $user;
    }
    
    $totalPages = ceil($totalUsers / $perPage);
    
    $this->view('admin/users/index', [
        'users' => $userObjects,
        'currentPage' => $page,
        'totalPages' => $totalPages,
        'totalUsers' => $totalUsers,
        'search' => $search
    ]);
}
```

### 2. **UserController::update() - Множество N+1 проблеми**

**Проблем:**
```php
// UserController.php:239-242
$currentGroups = $user->getGroups();  // 1 заявка
foreach ($currentGroups as $group) {   // N заявки
    $user->removeFromGroup($group->id);  // 1 заявка за всяка група
}

// UserController.php:245-249
foreach ($data['groups'] as $groupId) {  // M заявки
    $user->addToGroup((int) $groupId, $currentUser['id']);  // 1 заявка за всяка група
}
```

**Решение:**
```php
// Оптимизирана версия
if ($this->app->hasPermission('users.manage_groups')) {
    // Премахни всички групи с една заявка
    Database::query(
        "DELETE FROM user_groups WHERE user_id = ?",
        [$user->id]
    );
    
    // Добави новите групи с една заявка
    if (isset($data['groups']) && is_array($data['groups'])) {
        $currentUser = $this->app->getCurrentUser();
        $values = [];
        $placeholders = [];
        
        foreach ($data['groups'] as $groupId) {
            $values[] = $user->id;
            $values[] = (int) $groupId;
            $values[] = $currentUser['id'];
            $values[] = date('Y-m-d H:i:s');
            $placeholders[] = "(?, ?, ?, ?)";
        }
        
        if (!empty($placeholders)) {
            Database::query(
                "INSERT INTO user_groups (user_id, group_id, assigned_by, assigned_at) VALUES " . 
                implode(", ", $placeholders),
                $values
            );
        }
    }
}
```

### 3. **GroupController::index() - Същия проблем като UserController**

**Проблем:**
```php
// GroupController.php:25-27
$groups = Group::all();
$totalGroups = count($groups);
$groups = array_slice($groups, ($page - 1) * $perPage, $perPage);
```

**Решение:** Същата оптимизация като за UserController

### 4. **Model::all() - Неефективен за големи таблици**

**Проблем:**
```php
// Model.php:60-74
public static function all(): array
{
    $instance = new static();
    $results = Database::fetchAll("SELECT * FROM {$instance->table}");  // Зарежда ВСИЧКИ записи
    // ...
}
```

**Решение:**
```php
// Добави методи за пагинация
public static function paginate(int $page = 1, int $perPage = 20, array $conditions = []): array
{
    $instance = new static();
    $offset = ($page - 1) * $perPage;
    
    $whereClause = '';
    $params = [];
    
    if (!empty($conditions)) {
        $whereParts = [];
        foreach ($conditions as $column => $value) {
            $whereParts[] = "$column = ?";
            $params[] = $value;
        }
        $whereClause = "WHERE " . implode(" AND ", $whereParts);
    }
    
    $results = Database::fetchAll(
        "SELECT * FROM {$instance->table} $whereClause LIMIT $perPage OFFSET $offset",
        $params
    );
    
    $instances = [];
    foreach ($results as $data) {
        $newInstance = new static();
        $newInstance->fill($data);
        $instances[] = $newInstance;
    }
    
    return $instances;
}

public static function count(array $conditions = []): int
{
    $instance = new static();
    
    $whereClause = '';
    $params = [];
    
    if (!empty($conditions)) {
        $whereParts = [];
        foreach ($conditions as $column => $value) {
            $whereParts[] = "$column = ?";
            $params[] = $value;
        }
        $whereClause = "WHERE " . implode(" AND ", $whereParts);
    }
    
    return (int) Database::fetchColumn(
        "SELECT COUNT(*) FROM {$instance->table} $whereClause",
        $params
    );
}
```

## ✅ Добри практики (вече приложени)

### 1. **TaskController::index() - Отлична оптимизация**
- Използва JOIN-ове за избягване на N+1
- Правилна пагинация в SQL
- Предварително изчисляване на computed properties

### 2. **Task::getTasksForUser() - Добра оптимизация**
- Комплексна заявка с множество JOIN-ове
- Избягва N+1 проблеми

## 🚀 Препоръки за подобрение

### 1. **Добавете индекси**
```sql
-- За търсене на потребители
CREATE INDEX idx_users_search ON users(first_name, last_name, username, email);

-- За user_groups таблицата
CREATE INDEX idx_user_groups_user_id ON user_groups(user_id);
CREATE INDEX idx_user_groups_group_id ON user_groups(group_id);

-- За tasks таблицата
CREATE INDEX idx_tasks_assigned_to ON tasks(assigned_to);
CREATE INDEX idx_tasks_created_by ON tasks(created_by);
CREATE INDEX idx_tasks_status_id ON tasks(status_id);
CREATE INDEX idx_tasks_type_id ON tasks(task_type_id);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);
```

### 2. **Създайте базов PaginatedController**
```php
abstract class PaginatedController extends Controller
{
    protected function paginate(string $model, int $page = 1, int $perPage = 20, array $conditions = []): array
    {
        $totalCount = $model::count($conditions);
        $items = $model::paginate($page, $perPage, $conditions);
        $totalPages = ceil($totalCount / $perPage);
        
        return [
            'items' => $items,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalCount' => $totalCount,
            'perPage' => $perPage
        ];
    }
}
```

### 3. **Добавете query logging за мониторинг**
```php
// В Database класа
public static function query(string $sql, array $params = []): PDOStatement
{
    $start = microtime(true);
    $stmt = self::getConnection()->prepare($sql);
    $stmt->execute($params);
    $time = microtime(true) - $start;
    
    // Log slow queries
    if ($time > 0.1) { // Над 100ms
        error_log("Slow query ({$time}s): $sql");
    }
    
    return $stmt;
}
```

## 📊 Приоритети за поправка

1. **Високо** - UserController и GroupController пагинация
2. **Средно** - User groups update оптимизация  
3. **Ниско** - Добавяне на индекси
4. **Ниско** - Query logging система
