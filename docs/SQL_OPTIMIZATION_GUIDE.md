# SQL Оптимизация и N+1 Проблеми - Ръководство

## Общи принципи за оптимизация

### 1. Избягвайте N+1 проблеми

**Проблем**: Извикване на допълнителни SQL заявки в цикъл
```php
// ❌ ГРЕШНО - N+1 проблем
$tasks = Task::all();
foreach ($tasks as $task) {
    echo $task->getCreator()->name; // Нова SQL заявка за всяка задача
    echo $task->getTaskType()->name; // Още една заявка
}
```

**Решение**: Използвайте JOIN-ове за зареждане на свързаните данни
```php
// ✅ ПРАВИЛНО - Една оптимизирана заявка
$sql = "SELECT t.*, 
               u.first_name, u.last_name,
               tt.name as type_name
        FROM tasks t
        INNER JOIN users u ON t.created_by = u.id
        INNER JOIN task_types tt ON t.task_type_id = tt.id";
```

### 2. Предварително изчисляване на стойности

**Проблем**: Повтарящи се изчисления във view файловете
```php
// ❌ ГРЕШНО - Изчисления в цикъл
foreach ($tasks as $task) {
    echo $task->getPriorityColor(); // Изчисление за всяка задача
    echo $task->isOverdue(); // Още едно изчисление
}
```

**Решение**: Изчислете стойностите в контролера
```php
// ✅ ПРАВИЛНО - Предварително изчисляване
foreach ($results as $data) {
    $task = new Task();
    // ... populate task
    $task->priority_color = $this->getPriorityColor($task->priority);
    $task->is_overdue = $this->isOverdue($task->due_date);
}
```

### 3. Използвайте индекси правилно

**Основни правила за индекси**:
- Добавяйте индекси на колони, които се използват в WHERE клаузи
- Добавяйте индекси на foreign key колони
- Добавяйте композитни индекси за често използвани комбинации
- Не добавяйте прекалено много индекси (забавят INSERT/UPDATE)

```sql
-- ✅ ПРАВИЛНО - Индекси за оптимизация
CREATE TABLE tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    status_id INT NOT NULL,
    assigned_to INT NULL,
    due_date DATE NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_status (status_id),           -- За филтриране по статус
    INDEX idx_assigned (assigned_to),       -- За филтриране по изпълнител
    INDEX idx_due_date (due_date),         -- За филтриране по срок
    INDEX idx_created_at (created_at),     -- За сортиране
    INDEX idx_status_due (status_id, due_date) -- Композитен за просрочени задачи
);
```

## Чеклист за нови модули

### При създаване на нов модул, проверете:

#### 1. Структура на базата данни
- [ ] Всички foreign key колони имат индекси
- [ ] Колони за филтриране имат индекси
- [ ] Колони за сортиране имат индекси
- [ ] Композитни индекси за често използвани комбинации

#### 2. Модели (Models)
- [ ] Методи за зареждане на списъци използват JOIN-ове
- [ ] Избягвайте lazy loading в цикли
- [ ] Предоставете методи за eager loading на свързани данни

#### 3. Контролери (Controllers)
- [ ] Списъчни методи използват оптимизирани заявки с JOIN-ове
- [ ] Предварително изчисляване на често използвани стойности
- [ ] Пагинация за големи списъци
- [ ] Филтриране се прави на SQL ниво, не в PHP

#### 4. View файлове
- [ ] Избягвайте извикване на методи, които правят SQL заявки
- [ ] Използвайте предварително изчислени стойности
- [ ] Минимизирайте логиката във view файловете

## Примери за оптимизация

### Пример 1: Списък със задачи

```php
// ❌ ГРЕШНО - Множество заявки
public function index() {
    $tasks = Task::all(); // 1 заявка
    foreach ($tasks as $task) {
        $task->creator; // N заявки
        $task->type;    // N заявки
        $task->status;  // N заявки
    }
}

// ✅ ПРАВИЛНО - Една оптимизирана заявка
public function index() {
    $sql = "SELECT t.*, 
                   u.first_name, u.last_name,
                   tt.name as type_name, tt.color as type_color,
                   ts.name as status_name, ts.color as status_color
            FROM tasks t
            INNER JOIN users u ON t.created_by = u.id
            INNER JOIN task_types tt ON t.task_type_id = tt.id
            INNER JOIN task_statuses ts ON t.status_id = ts.id
            ORDER BY t.created_at DESC
            LIMIT 20";
    
    $results = Database::fetchAll($sql);
    // Обработка на резултатите...
}
```

### Пример 2: Филтриране и търсене

```php
// ❌ ГРЕШНО - Филтриране в PHP
public function search($query) {
    $allTasks = Task::all(); // Зарежда всички записи
    return array_filter($allTasks, function($task) use ($query) {
        return strpos($task->title, $query) !== false;
    });
}

// ✅ ПРАВИЛНО - Филтриране в SQL
public function search($query) {
    $sql = "SELECT t.*, tt.name as type_name, ts.name as status_name
            FROM tasks t
            INNER JOIN task_types tt ON t.task_type_id = tt.id
            INNER JOIN task_statuses ts ON t.status_id = ts.id
            WHERE t.title LIKE ? OR t.description LIKE ?
            ORDER BY t.created_at DESC";
    
    return Database::fetchAll($sql, ["%$query%", "%$query%"]);
}
```

## Инструменти за мониторинг

### 1. MySQL Query Log
```sql
-- Включване на slow query log
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 1; -- Заявки над 1 секунда
```

### 2. EXPLAIN за анализ на заявки
```sql
-- Анализ на заявка
EXPLAIN SELECT t.*, u.name 
FROM tasks t 
INNER JOIN users u ON t.created_by = u.id 
WHERE t.status_id = 1;
```

### 3. Профилиране в PHP
```php
// Измерване на времето за изпълнение
$start = microtime(true);
$results = Database::fetchAll($sql, $params);
$time = microtime(true) - $start;
error_log("Query took: " . $time . " seconds");
```

## Заключение

Винаги мислете за производителността при проектиране на нови модули:
1. Планирайте индексите заедно със схемата
2. Оптимизирайте заявките с JOIN-ове
3. Избягвайте N+1 проблеми
4. Предварително изчислявайте сложни стойности
5. Тествайте с реални данни

Помнете: Преждевременната оптимизация е корен на всички зла, но планирането за производителност от самото начало е мъдро решение.
