<?php

/**
 * Debug login process
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

use Strix\ERP\Core\Database;
use Strix\ERP\Models\User;

// Initialize database
$config = require __DIR__ . '/config/database.php';
Database::setConfig($config);

echo "=== DEBUG LOGIN PROCESS ===\n\n";

// Test database connection
try {
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    echo "✓ Database connection successful\n";
} catch (Exception $e) {
    echo "✗ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Test finding user
$username = 'admin';
echo "\n--- Testing User::findByUsername('$username') ---\n";

try {
    $user = User::findByUsername($username);
    if ($user) {
        echo "✓ User found\n";
        echo "  ID: {$user->id}\n";
        echo "  Username: {$user->username}\n";
        echo "  Email: {$user->email}\n";
        echo "  Is Active: " . ($user->isActive() ? 'Yes' : 'No') . "\n";
        echo "  Is Locked: " . ($user->isLocked() ? 'Yes' : 'No') . "\n";
        echo "  Failed Attempts: {$user->failed_login_attempts}\n";
        echo "  Password Hash: " . substr($user->password_hash, 0, 30) . "...\n";
    } else {
        echo "✗ User not found\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "✗ Error finding user: " . $e->getMessage() . "\n";
    exit(1);
}

// Test password verification
$password = 'admin123';
echo "\n--- Testing password verification ---\n";
echo "Password to test: '$password'\n";

try {
    $result = $user->verifyPassword($password);
    if ($result) {
        echo "✓ Password verification successful\n";
    } else {
        echo "✗ Password verification failed\n";
        
        // Test with direct password_verify
        echo "\n--- Testing direct password_verify ---\n";
        $directResult = password_verify($password, $user->password_hash);
        if ($directResult) {
            echo "✓ Direct password_verify successful\n";
        } else {
            echo "✗ Direct password_verify failed\n";
        }
    }
} catch (Exception $e) {
    echo "✗ Error verifying password: " . $e->getMessage() . "\n";
}

// Test user groups
echo "\n--- Testing user groups ---\n";
try {
    $groups = $user->getGroups();
    echo "User groups: " . count($groups) . "\n";
    foreach ($groups as $group) {
        echo "  - {$group->name}\n";
    }
    
    $isAdmin = $user->hasGroup('administrators');
    echo "Is administrator: " . ($isAdmin ? 'Yes' : 'No') . "\n";
} catch (Exception $e) {
    echo "✗ Error getting user groups: " . $e->getMessage() . "\n";
}

// Test permissions
echo "\n--- Testing user permissions ---\n";
try {
    $permissions = $user->getPermissionNames();
    echo "User permissions: " . count($permissions) . "\n";
    foreach (array_slice($permissions, 0, 5) as $permission) {
        echo "  - $permission\n";
    }
    if (count($permissions) > 5) {
        echo "  ... and " . (count($permissions) - 5) . " more\n";
    }
} catch (Exception $e) {
    echo "✗ Error getting user permissions: " . $e->getMessage() . "\n";
}

echo "\n=== DEBUG COMPLETE ===\n";
