<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Database;
use Strix\ERP\Models\User;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

try {
    echo "🔍 Проверка на правата за задачи...\n\n";
    
    $config = require __DIR__ . '/../config/database.php';
    Database::setConfig($config);
    
    // Check admin user permissions
    $admin = User::findByUsername('admin');
    if ($admin) {
        echo "👤 Администратор намерен: {$admin->getFullName()}\n";
        
        $permissions = $admin->getPermissionNames();
        echo "📋 Общо права: " . count($permissions) . "\n";
        
        $taskPermissions = array_filter($permissions, fn($p) => strpos($p, 'tasks.') === 0);
        echo "📋 Права за задачи: " . count($taskPermissions) . "\n";
        
        if (!empty($taskPermissions)) {
            foreach ($taskPermissions as $permission) {
                echo "  ✓ $permission\n";
            }
        } else {
            echo "  ❌ Няма права за задачи\n";
        }
        
        // Check groups
        $groups = $admin->getGroups();
        echo "\n👥 Групи на администратора:\n";
        foreach ($groups as $group) {
            echo "  - {$group->name}\n";
            $groupPermissions = $group->getPermissionNames();
            $groupTaskPermissions = array_filter($groupPermissions, fn($p) => strpos($p, 'tasks.') === 0);
            echo "    Права за задачи: " . count($groupTaskPermissions) . "\n";
            foreach ($groupTaskPermissions as $permission) {
                echo "      ✓ $permission\n";
            }
        }
    } else {
        echo "❌ Администраторът не е намерен\n";
    }
    
    // Check all task permissions in system
    echo "\n📋 Всички права за задачи в системата:\n";
    $allTaskPermissions = Database::fetchAll(
        "SELECT name, description FROM permissions WHERE module = 'tasks' ORDER BY name"
    );
    
    foreach ($allTaskPermissions as $permission) {
        echo "  - {$permission['name']}: {$permission['description']}\n";
    }
    
    // Check administrators group permissions
    echo "\n👥 Права на групата 'administrators':\n";
    $adminGroupPermissions = Database::fetchAll(
        "SELECT p.name, p.description 
         FROM permissions p
         INNER JOIN group_permissions gp ON p.id = gp.permission_id
         INNER JOIN groups g ON gp.group_id = g.id
         WHERE g.name = 'administrators' AND p.module = 'tasks'
         ORDER BY p.name"
    );
    
    if (!empty($adminGroupPermissions)) {
        foreach ($adminGroupPermissions as $permission) {
            echo "  ✓ {$permission['name']}: {$permission['description']}\n";
        }
    } else {
        echo "  ❌ Групата 'administrators' няма права за задачи\n";
        
        // Let's fix this
        echo "\n🔧 Добавяне на права за задачи към групата 'administrators'...\n";
        
        $sql = "INSERT INTO group_permissions (group_id, permission_id, granted_by)
                SELECT 
                    (SELECT id FROM groups WHERE name = 'administrators') as group_id,
                    p.id as permission_id,
                    (SELECT id FROM users WHERE username = 'admin') as granted_by
                FROM permissions p
                WHERE p.module = 'tasks'
                ON DUPLICATE KEY UPDATE granted_by = VALUES(granted_by)";
        
        Database::query($sql);
        echo "  ✓ Правата са добавени успешно\n";
    }
    
} catch (Exception $e) {
    echo "❌ Грешка: " . $e->getMessage() . "\n";
    exit(1);
}
