<?php

/**
 * Create admin user with proper password hash
 */

require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

$config = require __DIR__ . '/../config/database.php';

try {
    // Connect to database
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    
    echo "Свързване с база данни... ✓\n";
    
    // Generate proper password hash
    $password = 'admin123';
    $passwordHash = password_hash($password, PASSWORD_DEFAULT);
    
    echo "Генериране на hash за парола... ✓\n";
    
    // Insert or update admin user
    $sql = "INSERT INTO users (username, email, password_hash, first_name, last_name, is_active) VALUES
            ('admin', '<EMAIL>', ?, 'Администратор', 'Системен', TRUE)
            ON DUPLICATE KEY UPDATE 
            password_hash = VALUES(password_hash),
            email = VALUES(email),
            first_name = VALUES(first_name),
            last_name = VALUES(last_name),
            is_active = VALUES(is_active),
            failed_login_attempts = 0,
            locked_until = NULL";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$passwordHash]);
    
    echo "Администраторски потребител е създаден/обновен успешно! ✓\n";
    echo "\nДанни за вход:\n";
    echo "Потребителско име: admin\n";
    echo "Парола: admin123\n";
    echo "\nHash на паролата: $passwordHash\n";
    
    // Verify the hash works
    if (password_verify($password, $passwordHash)) {
        echo "Верификация на паролата: ✓ Успешна\n";
    } else {
        echo "Верификация на паролата: ✗ Неуспешна\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Грешка при свързване с базата данни: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ Неочаквана грешка: " . $e->getMessage() . "\n";
    exit(1);
}
