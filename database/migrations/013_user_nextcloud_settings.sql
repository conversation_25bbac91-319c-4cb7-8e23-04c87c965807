-- User Nextcloud Settings Table
-- This table stores personal Nextcloud configuration for each user

CREATE TABLE IF NOT EXISTS user_nextcloud_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    
    -- Nextcloud server configuration
    server_url VARCHAR(255) NULL COMMENT 'Nextcloud server URL (e.g., https://cloud.example.com)',
    username VARCHAR(100) NULL COMMENT 'Nextcloud username',
    password TEXT NULL COMMENT 'Encrypted Nextcloud app password',
    
    -- Connection settings
    verify_ssl BOOLEAN DEFAULT TRUE COMMENT 'Whether to verify SSL certificates',
    timeout INT DEFAULT 30 COMMENT 'Connection timeout in seconds',
    
    -- User preferences
    default_folder VARCHAR(255) DEFAULT '/' COMMENT 'Default folder for file operations',
    auto_create_folders BOOLEAN DEFAULT TRUE COMMENT 'Auto-create folders if they do not exist',
    
    -- Status and metadata
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Whether this configuration is active',
    last_tested_at TIMESTAMP NULL COMMENT 'When the connection was last tested',
    last_test_result ENUM('success', 'failed', 'pending') DEFAULT 'pending' COMMENT 'Result of last connection test',
    last_error_message TEXT NULL COMMENT 'Last error message if connection failed',
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NULL,
    updated_by INT NULL,
    
    -- Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_is_active (is_active),
    INDEX idx_last_tested (last_tested_at),
    
    -- Ensure one active configuration per user
    UNIQUE KEY unique_active_per_user (user_id, is_active)
);

-- Add comments to the table
ALTER TABLE user_nextcloud_settings COMMENT = 'Personal Nextcloud configuration settings for each user';

-- Insert default settings for existing admin user
INSERT INTO user_nextcloud_settings (
    user_id, 
    server_url, 
    username, 
    password,
    default_folder,
    is_active,
    created_by
) 
SELECT 
    u.id,
    CASE 
        WHEN LENGTH(COALESCE(@nextcloud_url, '')) > 0 THEN @nextcloud_url
        ELSE NULL 
    END,
    CASE 
        WHEN LENGTH(COALESCE(@nextcloud_username, '')) > 0 THEN @nextcloud_username
        ELSE NULL 
    END,
    CASE
        WHEN LENGTH(COALESCE(@nextcloud_password, '')) > 0 THEN TO_BASE64(@nextcloud_password)
        ELSE NULL
    END,
    '/ERP_Files',
    TRUE,
    u.id
FROM users u 
WHERE u.username = 'admin'
AND NOT EXISTS (
    SELECT 1 FROM user_nextcloud_settings uns WHERE uns.user_id = u.id
);

-- Create a view for easier access to decrypted settings
CREATE OR REPLACE VIEW user_nextcloud_settings_view AS
SELECT
    uns.id,
    uns.user_id,
    u.username,
    CONCAT(u.first_name, ' ', u.last_name) as full_name,
    uns.server_url,
    uns.username as nextcloud_username,
    CASE
        WHEN uns.password IS NOT NULL THEN
            CAST(FROM_BASE64(uns.password) AS CHAR)
        ELSE NULL
    END as nextcloud_password,
    uns.verify_ssl,
    uns.timeout,
    uns.default_folder,
    uns.auto_create_folders,
    uns.is_active,
    uns.last_tested_at,
    uns.last_test_result,
    uns.last_error_message,
    uns.created_at,
    uns.updated_at
FROM user_nextcloud_settings uns
INNER JOIN users u ON uns.user_id = u.id;

-- Add permissions for managing personal Nextcloud settings
INSERT INTO permissions (name, description, module, action) VALUES
('nextcloud.manage_personal', 'Управление на лични Nextcloud настройки', 'nextcloud', 'manage_personal'),
('nextcloud.test_connection', 'Тестване на Nextcloud връзка', 'nextcloud', 'test_connection'),
('nextcloud.view_personal', 'Преглед на лични Nextcloud настройки', 'nextcloud', 'view_personal')
ON DUPLICATE KEY UPDATE description = VALUES(description);

-- Grant permissions to all groups (users should manage their own settings)
INSERT INTO group_permissions (group_id, permission_id, granted_by)
SELECT 
    g.id as group_id,
    p.id as permission_id,
    (SELECT id FROM users WHERE username = 'admin') as granted_by
FROM groups g
CROSS JOIN permissions p
WHERE p.module = 'nextcloud' 
AND p.action IN ('manage_personal', 'test_connection', 'view_personal')
ON DUPLICATE KEY UPDATE granted_by = VALUES(granted_by);
