-- Performance Indexes Migration
-- Добавя индекси за подобряване на производителността

-- Users table indexes (separate indexes to avoid key length issues)
CREATE INDEX IF NOT EXISTS idx_users_first_name ON users(first_name);
CREATE INDEX IF NOT EXISTS idx_users_last_name ON users(last_name);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);

-- Groups table indexes
CREATE INDEX IF NOT EXISTS idx_groups_name ON groups(name);
CREATE INDEX IF NOT EXISTS idx_groups_created_at ON groups(created_at);
CREATE INDEX IF NOT EXISTS idx_groups_is_active ON groups(is_active);

-- User Groups table indexes
CREATE INDEX IF NOT EXISTS idx_user_groups_user_id ON user_groups(user_id);
CREATE INDEX IF NOT EXISTS idx_user_groups_group_id ON user_groups(group_id);
CREATE INDEX IF NOT EXISTS idx_user_groups_assigned_by ON user_groups(assigned_by);
CREATE INDEX IF NOT EXISTS idx_user_groups_assigned_at ON user_groups(assigned_at);

-- Tasks table indexes
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to ON tasks(assigned_to);
CREATE INDEX IF NOT EXISTS idx_tasks_created_by ON tasks(created_by);
CREATE INDEX IF NOT EXISTS idx_tasks_status_id ON tasks(status_id);
CREATE INDEX IF NOT EXISTS idx_tasks_type_id ON tasks(task_type_id);
CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks(due_date);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at);
CREATE INDEX IF NOT EXISTS idx_tasks_priority ON tasks(priority);

-- Task Assignees table indexes
CREATE INDEX IF NOT EXISTS idx_task_assignees_task_id ON task_assignees(task_id);
CREATE INDEX IF NOT EXISTS idx_task_assignees_user_id ON task_assignees(user_id);
CREATE INDEX IF NOT EXISTS idx_task_assignees_group_id ON task_assignees(group_id);

-- Task Types table indexes
CREATE INDEX IF NOT EXISTS idx_task_types_is_active ON task_types(is_active);
CREATE INDEX IF NOT EXISTS idx_task_types_name ON task_types(name);

-- Task Statuses table indexes
CREATE INDEX IF NOT EXISTS idx_task_statuses_is_active ON task_statuses(is_active);
CREATE INDEX IF NOT EXISTS idx_task_statuses_is_final ON task_statuses(is_final);

-- Permissions table indexes
CREATE INDEX IF NOT EXISTS idx_permissions_module ON permissions(module);
CREATE INDEX IF NOT EXISTS idx_permissions_name ON permissions(name);

-- Group Permissions table indexes
CREATE INDEX IF NOT EXISTS idx_group_permissions_group_id ON group_permissions(group_id);
CREATE INDEX IF NOT EXISTS idx_group_permissions_permission_id ON group_permissions(permission_id);

-- User Nextcloud Settings table indexes (if exists)
CREATE INDEX IF NOT EXISTS idx_user_nextcloud_settings_user_id ON user_nextcloud_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_nextcloud_settings_is_active ON user_nextcloud_settings(is_active);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_tasks_status_assignee ON tasks(status_id, assigned_to);
CREATE INDEX IF NOT EXISTS idx_tasks_type_status ON tasks(task_type_id, status_id);
CREATE INDEX IF NOT EXISTS idx_tasks_due_status ON tasks(due_date, status_id);
CREATE INDEX IF NOT EXISTS idx_user_groups_user_group ON user_groups(user_id, group_id);

-- Full-text search indexes (if supported)
-- ALTER TABLE users ADD FULLTEXT(first_name, last_name, username, email);
-- ALTER TABLE groups ADD FULLTEXT(name, description);
-- ALTER TABLE tasks ADD FULLTEXT(title, description);
