-- Storage permissions
INSERT INTO permissions (name, description, module, action) VALUES
('storage.view', 'Преглед на файлове и папки', 'storage', 'view'),
('storage.upload', 'Качване на файлове', 'storage', 'upload'),
('storage.download', 'Изтегляне на файлове', 'storage', 'download'),
('storage.create', 'Създаване на папки', 'storage', 'create'),
('storage.edit', 'Редактиране на файлове и папки', 'storage', 'edit'),
('storage.delete', 'Изтриване на файлове и папки', 'storage', 'delete'),
('storage.share', 'Споделяне на файлове', 'storage', 'share'),
('storage.admin', 'Администриране на файловото хранилище', 'storage', 'admin')
ON DUPLICATE KEY UPDATE description = VALUES(description);

-- Grant storage permissions to administrators group
INSERT INTO group_permissions (group_id, permission_id, granted_by)
SELECT 
    (SELECT id FROM groups WHERE name = 'administrators') as group_id,
    p.id as permission_id,
    (SELECT id FROM users WHERE username = 'admin') as granted_by
FROM permissions p
WHERE p.module = 'storage'
ON DUPLICATE KEY UPDATE granted_by = VALUES(granted_by);

-- Grant basic storage permissions to managers group
INSERT INTO group_permissions (group_id, permission_id, granted_by)
SELECT 
    (SELECT id FROM groups WHERE name = 'managers') as group_id,
    p.id as permission_id,
    (SELECT id FROM users WHERE username = 'admin') as granted_by
FROM permissions p
WHERE p.module = 'storage' AND p.action IN ('view', 'upload', 'download', 'create')
ON DUPLICATE KEY UPDATE granted_by = VALUES(granted_by);

-- Grant view and download permissions to users group
INSERT INTO group_permissions (group_id, permission_id, granted_by)
SELECT 
    (SELECT id FROM groups WHERE name = 'users') as group_id,
    p.id as permission_id,
    (SELECT id FROM users WHERE username = 'admin') as granted_by
FROM permissions p
WHERE p.module = 'storage' AND p.action IN ('view', 'download')
ON DUPLICATE KEY UPDATE granted_by = VALUES(granted_by);
