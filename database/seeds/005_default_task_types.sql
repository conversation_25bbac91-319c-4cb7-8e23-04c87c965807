-- Insert default task types
INSERT INTO task_types (name, description, color, icon) VALUES
('Разработка', 'Задачи свързани с разработка на софтуер', '#3498db', 'code'),
('Тестване', 'Задачи за тестване и качествен контрол', '#e74c3c', 'bug'),
('Документация', 'Създаване и поддръжка на документация', '#f39c12', 'file-text'),
('Поддръжка', 'Поддръжка на съществуващи системи', '#27ae60', 'wrench'),
('Анализ', 'Анализ на изисквания и планиране', '#9b59b6', 'chart-line'),
('Дизайн', 'UI/UX дизайн и графични задачи', '#e91e63', 'palette'),
('Администрация', 'Административни задачи', '#34495e', 'cog'),
('Обучение', 'Обучение и трансфер на знания', '#16a085', 'graduation-cap'),
('Среща', 'Срещи и презентации', '#f1c40f', 'users'),
('Общо', 'Общи задачи', '#95a5a6', 'task')

ON DUPLICATE KEY UPDATE 
description = VALUES(description),
color = VALUES(color),
icon = VALUES(icon);
