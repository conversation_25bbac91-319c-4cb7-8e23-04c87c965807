<?php

/**
 * Test authentication functionality
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Models\User;
use Strix\ERP\Core\Database;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

try {
    echo "🔍 Тестване на автентикацията...\n\n";
    
    // Test database connection
    echo "1. Тестване на връзката с базата данни...\n";
    $config = require __DIR__ . '/../config/database.php';
    Database::setConfig($config);
    $pdo = Database::getInstance();
    echo "   ✓ Връзката с базата данни е успешна\n\n";
    
    // Test finding user by username
    echo "2. Търсене на потребител 'admin'...\n";
    $user = User::findByUsername('admin');
    
    if (!$user) {
        echo "   ❌ Потребителят 'admin' не е намерен\n";
        exit(1);
    }
    
    echo "   ✓ Потребителят е намерен\n";
    echo "   - ID: {$user->id}\n";
    echo "   - Username: {$user->username}\n";
    echo "   - Email: {$user->email}\n";
    echo "   - Full Name: {$user->getFullName()}\n";
    echo "   - Is Active: " . ($user->isActive() ? 'Да' : 'Не') . "\n";
    echo "   - Password Hash: {$user->password_hash}\n\n";
    
    // Test password verification
    echo "3. Тестване на верификация на парола...\n";
    $password = 'admin123';
    
    if ($user->verifyPassword($password)) {
        echo "   ✓ Паролата '{$password}' е правилна\n";
    } else {
        echo "   ❌ Паролата '{$password}' е грешна\n";
        
        // Try to debug the issue
        echo "   🔍 Дебъг информация:\n";
        echo "   - Въведена парола: '{$password}'\n";
        echo "   - Hash в базата: '{$user->password_hash}'\n";
        echo "   - password_verify резултат: " . (password_verify($password, $user->password_hash) ? 'true' : 'false') . "\n";
        
        // Test with different passwords
        $testPasswords = ['admin123', 'Admin123', 'ADMIN123', 'admin', '123'];
        echo "   - Тестване с различни пароли:\n";
        foreach ($testPasswords as $testPass) {
            $result = password_verify($testPass, $user->password_hash);
            echo "     '{$testPass}': " . ($result ? '✓' : '❌') . "\n";
        }
    }
    
    echo "\n4. Проверка на статуса на акаунта...\n";
    echo "   - Failed login attempts: " . ($user->failed_login_attempts ?? 0) . "\n";
    echo "   - Locked until: " . ($user->locked_until ?? 'Не е заключен') . "\n";
    echo "   - Is locked: " . ($user->isLocked() ? 'Да' : 'Не') . "\n";
    
    // Reset failed attempts if locked
    if ($user->isLocked() || ($user->failed_login_attempts ?? 0) > 0) {
        echo "\n5. Нулиране на неуспешните опити...\n";
        $user->resetFailedAttempts();
        echo "   ✓ Неуспешните опити са нулирани\n";
    }
    
    echo "\n🎉 Тестването завърши успешно!\n";
    
} catch (Exception $e) {
    echo "❌ Грешка: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
